import {ReactQuery} from "@src/@types";

export interface BoMaQuyenLoiContextProps {
  danhSachBoMaQuyenLoi: Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>;
  danhSachBoMaQuyenLoiCha: Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>;
  loading: boolean;
  tongSoDong: number;
  tongSoDongQLCha: number;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.IDanhMucSanPham>;
  filterParams: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang>>;
  layDanhSachBoMaQuyenLoi: (Params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => void;
  layDanhSachBoMaQuyenLoiCha: (Params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => void;
  layChiTietBoMaQuyenLoi: (Params: ReactQuery.IChiTietBoMaQuyenLoiParams) => Promise<CommonExecute.Execute.IChiTietBoMaQuyenLoi | null>;
  updateBoMaQuyenLoi: (Params: ReactQuery.IUpdateBoMaQuyenLoiParams) => Promise<number | null | undefined>;
  // getListSanPhamTheoDoiTac: (params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => Promise<void>;
  getListSanPhamTheoDoiTac: (params: ReactQuery.ILietKeSanPhamParams) => void;
  getListDoiTac: () => Promise<void>;
}
