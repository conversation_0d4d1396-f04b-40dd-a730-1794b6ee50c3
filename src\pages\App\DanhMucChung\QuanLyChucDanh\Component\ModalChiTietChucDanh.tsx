import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, message, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiChucDanh, TRANG_THAI_TAO_MOI_CHUC_DANH} from "../index.configs";
import {useQuanLyChucDanhContext} from "../index.context";
import {IModalChiTietChucDanhRef, ModalChiTietChucDanhProps} from "./Constant";

const {ma, ten, trang_thai, ma_doi_tac_ql, stt} = FormTaoMoiChucDanh;

const ModalChiTietChucDanhComponent = forwardRef<IModalChiTietChucDanhRef, ModalChiTietChucDanhProps>(({listChucDanh}: ModalChiTietChucDanhProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChucDanh?: CommonExecute.Execute.IChiTietChucDanh) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataChucDanh) setChiTietChucDanh(dataChucDanh);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietChucDanh, setChiTietChucDanh] = useState<CommonExecute.Execute.IChiTietChucDanh | null>(null);
  const {listDoiTac} = useQuanLyChucDanhContext();
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietChucDanh, searchChucDanh, loading} = useQuanLyChucDanhContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data
  useEffect(() => {
    if (chiTietChucDanh) {
      const arrFormData = [];
      for (const key in chiTietChucDanh) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietChucDanh,
          value: chiTietChucDanh[key as keyof CommonExecute.Execute.IChiTietChucDanh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietChucDanh]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietChucDanh(null);
    form.resetFields();
  }, [form]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateChucDanhParams = form.getFieldsValue(); //lấy ra values của form
      console.log("values", values, chiTietChucDanh);
      //với trường hợp tạo mới -> check mã đơn vị chi nhánh đã tồn tại
      if (!chiTietChucDanh) {
        for (let i = 0; i < listChucDanh.length; i++) {
          if (listChucDanh[i].ma === values.ma) {
            form.setFields([
              {
                name: "ma",
                errors: ["Mã đơn vị chi nhánh đã tổn tại!"],
              },
            ]);
            return;
          }
        }
      }
      const response = await capNhatChiTietChucDanh(values); //cập nhật lại đơn vị chi nhánh
      console.log("response", response);
      if (response === -1) {
        await searchChucDanh(); //lấy lại danh sách đơn vị chi nhánh
        message.success((!chiTietChucDanh ? "Tạo mới" : "Cập nhật") + " thành công ");
        closeModal();
      }
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // RENDER
  //get màu trạng thái sử dụng
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form
      form={form}
      layout="vertical"
      initialValues={
        {
          // doi_tac: chiTietChucDanh ? chiTietChucDanh.ma_doi_tac : ""
        }
      }>
      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {renderFormColum({...ma_doi_tac_ql, options: listDoiTac, disabled: chiTietChucDanh ? true : false}, 8)}
        {renderFormColum({...ma, disabled: chiTietChucDanh ? true : false}, 8)}
        {renderFormColum({...ten}, 8)}
      </Row>

      {/* MÃ */}
      <Row gutter={16}>
        {renderFormColum({...stt})}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_CHUC_DANH})}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietChucDanh ? `${chiTietChucDanh.ten}` : "Tạo mới đối tác"} trang_thai_ten={chiTietChucDanh?.trang_thai_ten} trang_thai={chiTietChucDanh?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"60%"}
        styles={{
          body: {
            height: "100%",
            overflowY: "auto",
            overflowX: "hidden",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietChucDanhComponent.displayName = "ModalChiTietChucDanhComponent";
export const ModalChiTietChucDanh = memo(ModalChiTietChucDanhComponent, isEqual);
