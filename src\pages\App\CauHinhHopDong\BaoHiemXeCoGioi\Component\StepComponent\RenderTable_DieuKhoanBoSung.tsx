import {FormInput, InputCellTable} from "@src/components";
import {Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {memo, useMemo, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {parseNumber, tableDieuKhoanBoSungColumns, TableDieuKhoanBoSungDataType} from "./Constant";
import {formatCurrencyUS} from "@src/utils";
import {NGHIEP_VU_XE} from "../../index.configs";

interface RenderLoaiHinhNghiepVuTableProps {
  chiTietHopDongBaoHiem: CommonExecute.Execute.IHopDongXe;
  chiTietDoiTuongBaoHiemXe: CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi;
  onDataChange?: (data: TableDieuKhoanBoSungDataType[]) => void;
}

const RenderDieuKhoanBoSungTableComponent = (props: RenderLoaiHinhNghiepVuTableProps) => {
  const {listDieuKhoanBoSungXeCoGioi} = useBaoHiemXeCoGioiContext();
  const {chiTietHopDongBaoHiem, chiTietDoiTuongBaoHiemXe, onDataChange} = props;
  const [dieuKhoanBoSungData, setDieuKhoanBoSungData] = useState<TableDieuKhoanBoSungDataType[]>([]);
  const INPUT_FIELD_ARR = ["phi", "thue", "tong_phi", "phi_giam", "thue_giam"];

  //DATA BẢNG LOẠI HÌNH NV
  const listSanPhamBaoHiemXeCoGioi = useMemo<TableDieuKhoanBoSungDataType[]>(() => {
    const data = listDieuKhoanBoSungXeCoGioi
      .filter(item => item.nv === NGHIEP_VU_XE && item.ma_doi_tac_ql === chiTietHopDongBaoHiem?.ma_doi_tac_ql)
      .map((item, index) => {
        const dieuKhoanBoSungTheoGCNData = chiTietDoiTuongBaoHiemXe?.gcn_dkbs?.find((lhnv: any) => lhnv.ma === item.ma);
        const phi = parseNumber(dieuKhoanBoSungTheoGCNData?.phi);
        const thue = parseNumber(dieuKhoanBoSungTheoGCNData?.thue);
        const phi_giam = parseNumber(dieuKhoanBoSungTheoGCNData?.phi_giam);
        const thue_giam = parseNumber(dieuKhoanBoSungTheoGCNData?.thue_giam);
        return {
          ...item,
          key: item.key || index.toString(),
          stt: index + 1,
          ten: item.ten || "",
          ma: item.ma || "",
          ma_doi_tac: item.ma_doi_tac || "",
          ma_doi_tac_ql: item.ma_doi_tac_ql || "",
          nv: item.nv || "",
          phi: phi,
          thue: thue,
          tong_phi: phi + thue - phi_giam - thue_giam,
          t_gia: dieuKhoanBoSungTheoGCNData ? "C" : "K",
          tien: 0,
          loai: "",
          phi_giam,
          thue_giam,
        };
      });
    setDieuKhoanBoSungData(data);
    return data;
  }, [listDieuKhoanBoSungXeCoGioi, chiTietHopDongBaoHiem, chiTietDoiTuongBaoHiemXe]);

  //Hàm xử lý khi nhập tay
  const handleInputChange = (key: string, field: keyof TableDieuKhoanBoSungDataType, value: any) => {
    const updatedData = dieuKhoanBoSungData.map(item => {
      if (item.key === key) {
        const updatedItem = {...item, [field]: value};
        // Calculate tong_phi
        if (INPUT_FIELD_ARR.includes(field)) {
          const phi = field === "phi" ? parseNumber(value) : parseNumber(item.phi);
          const thue = field === "thue" ? parseNumber(value) : parseNumber(item.thue);
          const phi_giam = field === "phi_giam" ? parseNumber(value) : parseNumber(item.phi_giam);
          const thue_giam = field === "thue_giam" ? parseNumber(value) : parseNumber(item.thue_giam);
          updatedItem.tong_phi = phi + thue - phi_giam - thue_giam;
        }
        return updatedItem;
      }
      return item;
    });
    setDieuKhoanBoSungData(updatedData);
    //khi bấm check box cũng cập nhật luôn mảng
    if (field === "t_gia") {
      handleInputBlur(updatedData);
    }
  };

  //cập nhật mảng mới
  const handleInputBlur = (baseData: TableDieuKhoanBoSungDataType[]) => {
    const filteredData = baseData.filter(item => item.t_gia === "C");
    onDataChange?.(filteredData);
  };

  // Calculate totals
  const totals = dieuKhoanBoSungData.reduce(
    (acc, curr) => ({
      phi: acc.phi + parseNumber(curr.phi),
      thue: acc.thue + parseNumber(curr.thue),
      tong_phi: acc.tong_phi + parseNumber(curr.tong_phi),
      phi_giam: acc.phi_giam + parseNumber(curr.phi_giam),
      thue_giam: acc.thue_giam + parseNumber(curr.thue_giam),
      tong_phi_sau_giam: acc.tong_phi_sau_giam + (parseNumber(curr.phi) + parseNumber(curr.thue) - parseNumber(curr.phi_giam) - parseNumber(curr.thue_giam)),
    }),
    {phi: 0, thue: 0, tong_phi: 0, phi_giam: 0, thue_giam: 0, tong_phi_sau_giam: 0},
  );

  //render cột
  const renderColumn = (column: TableColumnType<TableDieuKhoanBoSungDataType>) => {
    if (!("dataIndex" in column)) return column;
    if (INPUT_FIELD_ARR.includes(column.dataIndex as string)) {
      return {
        ...column,
        render: (text: any, record: TableDieuKhoanBoSungDataType) => (
          //  input
          <FormInput
            className="border-b-1 !mb-0 !h-[20px] !border-t-0 border-b-gray-50"
            variant="underlined"
            component="input-price"
            onChange={(value: any) => handleInputChange(record.key, column.dataIndex as keyof TableDieuKhoanBoSungDataType, value?.target?.value ?? value)}
            onBlur={() => handleInputBlur(dieuKhoanBoSungData)}
            placeholder="0"
            disabled={column.dataIndex === "tong_phi"}
            value={text}
            allowClear={false}
          />
        ),
      };
    }
    //checkbox
    if (column.dataIndex === "t_gia") {
      return {
        ...column,
        render: (_: any, record: TableDieuKhoanBoSungDataType) => (
          <FormInput
            className="!mb-0"
            component="checkbox"
            checked={record.t_gia === "C"}
            onChange={(e: any) => {
              handleInputChange(record.key, "t_gia", e.target.checked ? "C" : "K");
            }}
          />
        ),
      };
    }

    return column;
  };

  //render tổng cộng của cột
  const renderSummaryCell = (index: number, value: string) => {
    return (
      <Table.Summary.Cell index={index} className="!p-[8px] !pr-[24px]">
        <div className="text-right font-medium">{value}</div>
      </Table.Summary.Cell>
    );
  };
  //render
  return (
    <div style={{overflowX: "auto"}}>
      <Table<TableDieuKhoanBoSungDataType>
        className="no-header-border-radius"
        columns={(tableDieuKhoanBoSungColumns || []).map(renderColumn)}
        dataSource={dieuKhoanBoSungData}
        pagination={false}
        scroll={{x: "max-content", y: 140}}
        bordered
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell className="!p-[8px]" index={0} colSpan={(tableDieuKhoanBoSungColumns || []).findIndex(col => "dataIndex" in col && col.dataIndex === "phi")}>
                <div className="text-center font-medium">Tổng cộng</div>
              </Table.Summary.Cell>
              {renderSummaryCell(1, formatCurrencyUS(totals.phi))}
              {renderSummaryCell(2, formatCurrencyUS(totals.thue))}
              {renderSummaryCell(3, formatCurrencyUS(totals.phi_giam))}
              {renderSummaryCell(4, formatCurrencyUS(totals.thue_giam))}
              {renderSummaryCell(5, formatCurrencyUS(totals.tong_phi_sau_giam))}
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );
};

RenderDieuKhoanBoSungTableComponent.displayName = "RenderDieuKhoanBoSungTableComponent";
export const RenderDieuKhoanBoSungTable = memo(RenderDieuKhoanBoSungTableComponent, isEqual);
