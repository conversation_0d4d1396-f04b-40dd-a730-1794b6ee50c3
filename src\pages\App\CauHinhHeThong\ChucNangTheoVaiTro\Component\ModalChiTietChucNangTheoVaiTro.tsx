import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import FormChiTietChucNangTheoVaiTro, {
  IModalChiTietChucNangTheoVaiTroRef,
  Props,
  TableChucNangChuaXacDinhDataType,
  TRANG_THAI,
  DataIndexChuaXacDinh,
  chucNangChuaXacDinhColumns,
  TableChucNangDataType,
  DataIndexChucNang,
  chucNangColumns,
} from "./index.configs";
import {Col, Flex, Form, Input, InputRef, Modal, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {useChucNangTheoVaiTroContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm} from "@src/components";
import {ArrowLeftOutlined, CheckOutlined, ClearOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import {COLOR_PALETTE} from "@src/constants";
const {ma, ten, stt, trang_thai} = FormChiTietChucNangTheoVaiTro;

const ModalChiTietChucNangTheoVaiTroComponent = forwardRef<IModalChiTietChucNangTheoVaiTroRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChucNangTheoVaiTro?: CommonExecute.Execute.IChiTietChucNangTheoVaiTro) => {
      setIsOpen(true);
      // if (dataChucNangTheoVaiTro) setChiTietChucNangTheoVaiTro(dataChucNangTheoVaiTro);
      console.log("dataChucNangTheoVaiTro", dataChucNangTheoVaiTro);
    },
    close: () => setIsOpen(false),
  }));
  // const [chiTietChucNangTheoVaiTro, setChiTietChucNangTheoVaiTro] = useState<CommonExecute.Execute.IChiTietChucNangTheoVaiTro | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedChucNang, setSelectedChucNang] = useState<string[]>([]);
  const {loading, onUpdateChucNangTheoVaiTro, filterParams, setFilterParams, danhSachChucNangTheoVaiTroPhanTrang, onDeleteChucNangTheoVaiTro, layChiTietChucNangTheoVaiTro, chiTietChucNangTheoVaiTro} =
    useChucNangTheoVaiTroContext();
  const searchInput = useRef<InputRef>(null);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const [form] = Form.useForm();
  const formValues = Form.useWatch([], form);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const pageSize = 8;
  useEffect(() => {
    if (chiTietChucNangTheoVaiTro) {
      const arrFormData = [];
      for (const key in chiTietChucNangTheoVaiTro.vtro_cn) {
        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IChiTietChucNangTheoVaiTro,
          value: (chiTietChucNangTheoVaiTro.vtro_cn as any)[key],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietChucNangTheoVaiTro, form]);
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = () => {
    setIsOpen(false);

    form.resetFields();
    setFilterParams(filterParams);
    setSelectedChucNang([]);
  };

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChuaXacDinh) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChuaXacDinh) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleSearchChucNang = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleResetChucNang = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexChucNang) => {
      clearFilters();
      setSearchedColumn("");
      handleSearchChucNang([""], confirm, dataIndex);
    },
    [handleSearchChucNang],
  );
  //Render
  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndexChuaXacDinh, title: string): TableColumnType<TableChucNangChuaXacDinhDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },

    render: (text, record, index) => {
      if (dataIndex === "is_checked") {
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return (
          <Tag color={color} className="text-[11px]">
            {text}
          </Tag>
        );
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  const getColumnChucNangSearchProps = (dataIndex: DataIndexChucNang, title: string): TableColumnType<TableChucNangDataType> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
      <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
        <Input
          ref={searchInput}
          placeholder={`Tìm theo ${title}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearchChucNang(selectedKeys as string[], confirm, dataIndex)}
          style={{display: "block", marginRight: 8}}
        />
        <Tooltip title="Tìm kiếm">
          <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearchChucNang(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
        </Tooltip>
        <Tooltip title="Xoá">
          <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleResetChucNang(clearFilters, confirm, dataIndex)} />
        </Tooltip>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },

    render: (text, record, index) => {
      // if (dataIndex === "hanh_dong") {

      //   if (record.key.toString().includes("empty")) return "";
      //   return (
      //     renderDeleteButton(chiTietChucNangTheoVaiTro?.vtro_cn?.[0]?.ma, record.ma)
      //   );
      // }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]" style={{height: 18}}>
          {"\u00A0"}
        </Tag>
      );
    },
  });

  const danhSachChucNangChuaXacDinh = chiTietChucNangTheoVaiTro?.ds_cn_chua_xd;
  const danhSachChucNang = chiTietChucNangTheoVaiTro?.ds_cn;
  useEffect(() => {
    console.log("chi tiết chức năng theo vai trò mã", chiTietChucNangTheoVaiTro?.vtro_cn?.ma, chiTietChucNangTheoVaiTro?.vtro_cn?.ten);
  }, [chiTietChucNangTheoVaiTro]);
  const listChucNangChuaXacDinh = useMemo<Array<TableChucNangChuaXacDinhDataType>>(() => {
    try {
      const tableData = (danhSachChucNangChuaXacDinh || []).map((item: any, index: number) => {
        return {
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          loai: item.loai,
          key: index.toString(),
          is_checked: selectedChucNang.includes(item.ma) ? "1" : "0",
        };
      });

      const arrEmptyRow: Array<TableChucNangChuaXacDinhDataType> = fillRowTableEmpty(tableData.length);

      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucNang chưa xác định error", error);
      return [];
    }
  }, [danhSachChucNangChuaXacDinh, selectedChucNang]);
  const dataTableListChucNang = useMemo<Array<TableChucNangDataType>>(() => {
    try {
      console.log("danhSachChucNang", danhSachChucNang);
      const tableData = (danhSachChucNang || []).map((item: any, index: number) => {
        return {
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          loai: item.loai,
          key: index.toString(),
          hanh_dong: () => renderDeleteButton(chiTietChucNangTheoVaiTro?.vtro_cn?.ma, item.ma) || null,
        };
      });

      const arrEmptyRow: Array<TableChucNangDataType> = fillRowTableEmpty(tableData.length, pageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListChucNang error", error);
      return [];
    }
  }, [danhSachChucNang]);
  const handleChucNangChuaXacDinhRowClick = (record: TableChucNangChuaXacDinhDataType) => {
    handleInputChange(record?.ma || "", "is_checked", record.is_checked === "1" ? "0" : "1");
  };
  const handleInputChange = (ma: string, field: keyof TableChucNangChuaXacDinhDataType, value: any) => {
    if (!ma) {
      return;
    }
    setSelectedChucNang(prev => {
      if (value === "1") {
        // Thêm hoặc cập nhật chức năng khi được chọn
        return prev.includes(ma) ? prev : [...prev, ma];
      } else {
        // Xóa chức năng khi bỏ chọn
        return prev.filter(p => p !== ma);
      }
    });
  };
  const onDelete = useCallback(
    async (ma: string, ma_chuc_nang: string) => {
      try {
        const params: ReactQuery.IDeleteChucNangTheoVaiTroParams = {
          ma: ma,
          ma_chuc_nang: ma_chuc_nang,
        };
        console.log("params", params);
        await onDeleteChucNangTheoVaiTro(params);
        layChiTietChucNangTheoVaiTro({ma: params.ma});
      } catch (error) {
        console.log("onDelete error", error);
      }
    },
    [onDeleteChucNangTheoVaiTro],
  );
  const renderDeleteButton = useCallback(
    (ma?: string, ma_chuc_nang?: string) => {
      console.log("renderdelettebutton");
      if (!ma || !ma_chuc_nang) return null;

      return (
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => onDelete(ma, ma_chuc_nang)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa chức năng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          variant="text"
          className="d-block h-auto"
          icon={<CloseOutlined />}
          buttonIcon
          style={{
            width: "fit-content",
          }}
        />
      );
    },
    [onDelete],
  );
  const renderColumn = (column: any) => {
    if (column.dataIndex === "is_checked") {
      return {
        ...column,
        render: (_: any, record: TableChucNangChuaXacDinhDataType) => {
          const isEmptyRow = !record.ma;
          // if (isEmptyRow) {
          //   return <div style={{height: 17}} />;
          // }
          return (
            <div className="custom-checkbox-cell" style={{height: 20, alignItems: "center", verticalAlign: "midle"}}>
              <FormInput
                className="!mb-0"
                component="checkbox"
                checked={record.is_checked === "1"}
                onChange={e => {
                  if (!record.ma) {
                    return;
                  }
                  handleInputChange(record.ma, "is_checked", e.target.checked ? "1" : "0");
                }}
              />
            </div>
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };
  const renderTableChucNangChuaXacDinh = () => {
    return (
      <Table<TableChucNangChuaXacDinhDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              // background: record.ma === selectedVaiTro ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleChucNangChuaXacDinhRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        // columns={(chucNangChuaXacDinhColumns || renderColumn).map(item => {
        //   // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
        //   return {
        //     ...item,
        //     ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableChucNangChuaXacDinhDataType, item.title) : {}),
        //   };
        // })}
        columns={(chucNangChuaXacDinhColumns || []).map(renderColumn)}
        rowClassName={(record, index) => (record.key.includes("empty") ? "empty-row" : "")}
        title={null}
        pagination={false}
        dataSource={listChucNangChuaXacDinh}
        scroll={{y: 230}}
      />
    );
  };
  const renderTableChucNang = () => {
    return (
      <Table<TableChucNangDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        columns={(chucNangColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnChucNangSearchProps(item.key as keyof TableChucNangDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListChucNang}
        title={null}
        pagination={false}
        // scroll={{y: 230}}
        scroll={dataTableListChucNang.length > pageSize ? {y: 230} : undefined}
      />
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={12} style={{paddingRight: 16}}>
          {renderTableChucNangChuaXacDinh()}
        </Col>
        <Col span={12}>{renderTableChucNang()}</Col>
      </Row>
    );
  };
  const renderFormInputColum = (props?: any, span = 6) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatChucNangTheoVaiTroParams = form.getFieldsValue(); //lấy ra values của form

      const params: ReactQuery.ICapNhatChucNangTheoVaiTroParams = {
        ...values,
        ds: selectedChucNang.map(row => ({
          ma: row,
        })),
      };
      console.log("params", params);
      const response = await onUpdateChucNangTheoVaiTro(params);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        console.log("value lấy chi tiết", values.ma);
        layChiTietChucNangTheoVaiTro({ma: values.ma});
        setSelectedChucNang([]);
      } else {
        console.log("cập nhật thất bại");
      }
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  //render footer
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: true})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
        {renderFormInputColum({...stt})}
      </Row>
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietChucNangTheoVaiTro ? `Chi tiết vai trò ${chiTietChucNangTheoVaiTro.vtro_cn?.ten} ` : "Tạo mới vai trò"}
            // trang_thai_ten={chiTietChucNangTheoVaiTro?.trang_thai_ten}
            // trang_thai={chiTietChucNangTheoVaiTro?.trang_thai}
          />
        }
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "80%",
          sm: "80%",
          md: "80%",
          lg: "80%",
          xl: "80%",
          xxl: "80%",
        }}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={renderFooter}
        className="modal-chi-tiet-chuc-nang-theo-vai-tro [&_.ant-space]:w-full">
        {renderForm()}
        {renderTable()}
      </Modal>
    </Flex>
  );
});
ModalChiTietChucNangTheoVaiTroComponent.displayName = "ModalChiTietChucNangTheoVaiTroComponent";
export const ModalChiTietChucNangTheoVaiTro = memo(ModalChiTietChucNangTheoVaiTroComponent, isEqual);
