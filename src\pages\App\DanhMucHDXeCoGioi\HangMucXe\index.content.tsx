import React, {memo, useCallback, useMemo, useRef, useState, useContext} from "react";
import {Col, Form, Input, InputRef, Row, Table, TableColumnType, Tag, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {PlusCircleOutlined, SearchOutlined, ClearOutlined, DeleteOutlined, EditOutlined, PlusOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {Button, FormInput, Highlighter, TableFilterDropdown} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";

import HangMucXeContext from "./index.context";
import {
  FormTimKiemDanhMucHangMucXe,
  tableHangMucXeColumn,
  TableHangMucXeColumnDataType,
  radioItemTrangThaiHangMucXeSelect,
  DANH_SACH_NGHIEP_VU_TIM_KIEM_HANG_MUC_XE,
  DANH_SACH_VI_TRI_TIM_KIEM_HANG_MUC_XE,
  DANH_SACH_LOAI_TIM_KIEM_HANG_MUC_XE,
} from "./index.configs";
import {ModalChiTietHangMucXe, IModalChiTietHangMucXeRef} from "./Component";
import "./index.default.scss";

type DataIndex = keyof TableHangMucXeColumnDataType;

// ===== DESTRUCTURING FORM SEARCH VALUES - EXTRACT CÁC FIELD TỪ CONFIG =====
const {nv, ma, ten, nhom, trang_thai} = FormTimKiemDanhMucHangMucXe;

/**
 * Component này chứa table danh sách hạng mục xe, form tìm kiếm, pagination và modal chi tiết
 */
const HangMucXeContent: React.FC = memo(() => {
  // ===== CONTEXT DATA - LẤY DỮ LIỆU VÀ METHODS TỪ CONTEXT =====
  const {listHangMucXe, listNhomHangMucXe, loading, tongSoDong, getChiTietHangMucXe, searchHangMucXe, filterParams, setFilterParams} = useContext(HangMucXeContext);

  // ===== STATE MANAGEMENT - QUẢN LÝ TRẠNG THÁI SEARCH VÀ MODAL =====
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState<DataIndex | "">("");
  const searchInput = useRef<HTMLInputElement>(null);
  const modalChiTietRef = useRef<IModalChiTietHangMucXeRef>(null);

  // ===== MEMO: DROPDOWN OPTIONS CHO NHÓM HẠNG MỤC XE =====
  const dropdownOptionsNhomHangMucXe = useMemo(() => {
    if (!listNhomHangMucXe || !Array.isArray(listNhomHangMucXe)) {
      return [{ma: "", ten: "Chọn nhóm hạng mục xe"}];
    }

    const allOption = [{ma: "", ten: "Tất cả"}];
    const validGroups = listNhomHangMucXe.filter(item => {
      const hasValidData = item && item.ma && item.ten;
      if (!hasValidData) return false;

      const trangThai = item.trang_thai?.toString();
      const isActive = trangThai === "1" || trangThai === "D" || trangThai === "Đang sử dụng" || !trangThai;

      return hasValidData && isActive;
    });

    return [...allOption, ...validGroups];
  }, [listNhomHangMucXe]);

  // ===== MEMO: DROPDOWN OPTIONS CHO NGHIỆP VỤ =====
  const dropdownOptionsNghiepVu = useMemo(() => {
    return DANH_SACH_NGHIEP_VU_TIM_KIEM_HANG_MUC_XE;
  }, []);
  const dropdownOptionsViTri = useMemo(() => {
    return DANH_SACH_VI_TRI_TIM_KIEM_HANG_MUC_XE;
  }, []);
  const dropdownOptionsLoai = useMemo(() => {
    return DANH_SACH_LOAI_TIM_KIEM_HANG_MUC_XE;
  }, []);
  // ===== MEMO: TRANSFORM DATA CHO TABLE VỚI STT =====
  const dataTableWithSTT = useMemo(() => {
    try {
      const currentPage = filterParams?.trang || 1;
      const currentPageSize = (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number;

      const tableData =
        listHangMucXe?.map((item, index) => ({
          ...item,
          key: item.ma && item.nv ? `${item.nv}-${item.ma}` : `row-${index}`,
          sott: (currentPage - 1) * currentPageSize + index + 1,
        })) || [];

      const arrEmptyRow = fillRowTableEmpty(tableData.length, currentPageSize);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableWithSTT error", error);
      return [];
    }
  }, [listHangMucXe, filterParams?.trang, filterParams?.so_dong]);

  // ===== HANDLER: XỬ LÝ SUBMIT FORM TÌM KIẾM =====
  const onSearchApi = useCallback(
    (values: ReactQuery.ITimKiemPhanTrangHangMucXeParams) => {
      // Xử lý submit form tìm kiếm và cập nhật filter parameters
      setFilterParams({
        nv: values.nv || "",
        ma: values.ma || "",
        ten: values.ten || "",
        loai: values.loai || "",
        nhom: values.nhom || "",
        vi_tri: values.vi_tri || "",
        trang_thai: values.trang_thai || "",
        trang: 1,
        so_dong: filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize,
      });
    },
    [setFilterParams, filterParams?.so_dong],
  );

  // ===== HANDLER: XỬ LÝ THAY ĐỔI PAGINATION =====
  const onChangePage = useCallback(
    (newPage: number, newPageSize: number) => {
      // Xử lý thay đổi trang và số dòng hiển thị trong pagination
      setFilterParams(prev => ({
        ...prev,
        trang: newPage,
        so_dong: newPageSize,
      }));
    },
    [setFilterParams],
  );

  // ===== HANDLER: XỬ LÝ TÌM KIẾM TRONG TABLE FILTER =====
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  // ===== HANDLER: XỬ LÝ RESET FILTER TRONG TABLE =====
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndex) => {
      clearFilters();
      setSearchText("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );

  // ===== HANDLER: XỬ LÝ CLICK VÀO ROW ĐỂ MỞ MODAL CHI TIẾT =====
  const handleRowClick = useCallback(
    async (record: TableHangMucXeColumnDataType) => {
      // Xử lý click vào row để hiển thị modal chi tiết hạng mục xe
      if (record.key.toString().includes("empty") || loading) return;

      try {
        if (!record.ma || !record.nv) {
          console.log("Thiếu thông tin mã hạng mục xe", {ma: record.ma, nv: record.nv});
          return;
        }

        const response = await getChiTietHangMucXe({
          ma: record.ma,
          nv: record.nv,
        });

        if (response && Object.keys(response).length > 0) {
          modalChiTietRef.current?.open(response);
        }
      } catch (error) {
        console.error("Error getting detail:", error);
      }
    },
    [getChiTietHangMucXe, loading],
  );

  // ===== HANDLER: XỬ LÝ CLICK NUT THÊM MỚI =====
  const handleThemMoi = useCallback(() => {
    // Mở modal để tạo mới hạng mục xe
    modalChiTietRef.current?.open();
  }, []);

  // ===== HANDLER: XỬ LÝ SAU KHI LƯU THÀNH CÔNG =====
  const handleAfterSave = useCallback(() => {
    // Reload danh sách sau khi lưu thành công
    searchHangMucXe();
  }, [searchHangMucXe]);

  // ===== RENDER FUNCTION: RENDER COLUMN INPUT TRONG FORM =====
  const renderFormInputColumn = useCallback(
    (props: IFormInput, span = 4) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    ),
    [],
  );

  // ===== RENDER FUNCTION: RENDER HEADER TABLE VỚI FORM TÌM KIẾM =====
  const renderHeaderTableHangMucXe = useCallback(
    () => (
      <Form layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={onSearchApi}>
        <Row gutter={16} align="bottom">
          {renderFormInputColumn(ma, 3)}
          {renderFormInputColumn(ten, 4)}
          {renderFormInputColumn({...nv, options: dropdownOptionsNghiepVu}, 4)}
          {/* {renderFormInputColumn(loai, 3)} */}
          {renderFormInputColumn({...nhom, options: dropdownOptionsNhomHangMucXe}, 4)}
          {/* {renderFormInputColumn(vi_tri, 3)} */}
          {renderFormInputColumn({...trang_thai, options: radioItemTrangThaiHangMucXeSelect}, 3)}
          <Col span={3}>
            <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} block>
              Tìm kiếm
            </Button>
          </Col>
          <Col span={3}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row>
        {/* <Row gutter={16} style={{marginTop: 8}}>
          <Col span={3}>
            <Button type="primary" block icon={<PlusCircleOutlined />} onClick={handleThemMoi}>
              Tạo mới
            </Button>
          </Col>
        </Row> */}
      </Form>
    ),
    [nv, ma, ten, nhom, trang_thai, dropdownOptionsNhomHangMucXe, dropdownOptionsNghiepVu, loading, onSearchApi, handleThemMoi, renderFormInputColumn],
  );

  // ===== FUNCTION: TẠO COLUMN SEARCH PROPS CHO TABLE =====
  const getColumnSearchProps = useCallback(
    (dataIndex: DataIndex, title: string): TableColumnType<TableHangMucXeColumnDataType> => ({
      filterDropdown:
        dataIndex !== "trang_thai_ten"
          ? ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
              <TableFilterDropdown
                ref={searchInput}
                title={title}
                selectedKeys={selectedKeys}
                dataIndex={dataIndex}
                setSelectedKeys={setSelectedKeys}
                handleSearch={handleSearch}
                confirm={confirm}
                clearFilters={clearFilters}
                handleReset={handleReset}
              />
            )
          : undefined,
      filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? COLOR_PALETTE.blue[60] : undefined}} />,
      // ===== THÊM DROPDOWN FILTER CHO CỘT TRẠNG THÁI =====
      filters:
        dataIndex === "trang_thai_ten"
          ? [
              {text: "Đang sử dụng", value: "Đang sử dụng"},
              {text: "Ngưng sử dụng", value: "Ngưng sử dụng"},
            ]
          : undefined,
      onFilter: (value, record) =>
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false,
      onFilterDropdownOpenChange: visible => {
        if (visible) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
      render: (text, record) => {
        if (dataIndex === "trang_thai_ten") {
          if (record.key?.toString().includes("empty") || !text) return "";
          const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
          return (
            <Tag color={color} className="text-[11px]">
              {text}
            </Tag>
          );
        }
        return searchedColumn === dataIndex ? (
          <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
        ) : text !== undefined ? (
          text
        ) : (
          <Tag color={"transparent"} className="!text-white text-[11px]">
            {"\u00A0"}
          </Tag>
        );
      },
    }),
    [handleSearch, handleReset, searchText, searchedColumn],
  );

  // ===== MAIN RENDER - RENDER TABLE VÀ MODAL =====
  return (
    <div id="HANG_MUC_XE" className="[&_.ant-space]:w-full">
      <Table<TableHangMucXeColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableWithSTT}
        columns={
          tableHangMucXeColumn?.map(col => {
            // Chỉ áp dụng search cho cột có dataIndex và không phải cột "sott" (STT)
            if ("dataIndex" in col && col.dataIndex && col.key !== "sott") {
              return {
                ...col,
                ...getColumnSearchProps(col.dataIndex as DataIndex, col.title as string),
              };
            }
            return col;
          }) || []
        }
        loading={loading}
        pagination={{
          ...defaultPaginationTableProps,
          current: filterParams?.trang || 1,
          pageSize: (filterParams?.so_dong || defaultPaginationTableProps.defaultPageSize) as number,
          total: tongSoDong,
          onChange: onChangePage,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} bản ghi`,
        }}
        title={renderHeaderTableHangMucXe}
        onRow={record => ({
          style: {cursor: loading ? "progress" : "pointer"},
          onClick: () => handleRowClick(record),
        })}
      />
      <ModalChiTietHangMucXe ref={modalChiTietRef} onAfterSave={handleAfterSave} danhSachNhomHangMucXe={listNhomHangMucXe} />
    </div>
  );
});

HangMucXeContent.displayName = "HangMucXeContent";
export default HangMucXeContent;
