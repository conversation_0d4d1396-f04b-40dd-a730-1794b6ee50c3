import {ReactQuery} from "@src/@types";

export interface ChucNangTheoVaiTroContextProps {
  loading: boolean;
  tongSoDong: number;
  // defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams;
  danhSachChucNangTheoVaiTroPhanTrang: Array<CommonExecute.Execute.IChucNangTheoVaiTro>;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams & ReactQuery.IPhanTrang;
  chiTietChucNangTheoVaiTro: CommonExecute.Execute.IChiTietChucNangTheoVaiTro;
  onDeleteChucNangTheoVaiTro: (item: ReactQuery.IDeleteChucNangTheoVaiTroParams) => Promise<number | null | undefined>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams & ReactQuery.IPhanTrang>>;
  onUpdateChucNangTheoVaiTro: (item: ReactQuery.ICapNhatChucNangTheoVaiTroParams) => Promise<number | null | undefined>;
  layDanhSachChucNangTheoVaiTroPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams) => void;
  layChiTietChucNangTheoVaiTro: (params: ReactQuery.IChiTietChucNangTheoVaiTroParams) => Promise<CommonExecute.Execute.IChiTietChucNangTheoVaiTro | null>;
}
