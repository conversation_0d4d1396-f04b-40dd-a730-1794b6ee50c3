import "@react-pdf-viewer/core/lib/styles/index.css";
import {IFormInput, ReactQuery} from "@src/@types";
import {FormInput, ModalQuanLyFileCaNhan} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {env} from "@src/utils";
import {Col, Form, message, Row, Table} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {NumericFormat} from "react-number-format";
import {useHopDongConNguoiContext} from "../index.context";
import {
  FileCategory,
  FormTimKiemNguoiDuocBaoHiem,
  IThemHopDongStep3Ref,
  NGHIEP_VU_NG,
  tableNguoiDuocBaoHiemColumn,
  TableNguoiDuocBaoHiemColumnDataType,
  ThemHopDongStep3Props,
  UNCLASSIFIED_ID,
  UNCLASSIFIED_NAME,
} from "./Constant";
import {FileSelectedView} from "./ThemHopDongStep3_FileSelectedView";
import {ListFileView} from "./ThemHopDongStep3_ListFileView";

const {nd_tim} = FormTimKiemNguoiDuocBaoHiem;

const getCategoryInfo = (file: any) => {
  if (!file.ma_hang_muc || file.ma_hang_muc === "HANG_MUC_KHAC") return {id: UNCLASSIFIED_ID, name: UNCLASSIFIED_NAME};
  return {id: file.ma_hang_muc, name: file.ten_hang_muc || file.ma_hang_muc};
};

const getFullUrl = (url: string, domain: string) => {
  if (!url) return "";
  if (url.startsWith("http://") || url.startsWith("https://")) return url;
  return domain.replace(/\/$/, "") + (url.startsWith("/") ? url : "/" + url);
};

// Sửa hàm xác định loại file
const getFileType = (extension?: string, url?: string): string => {
  let ext = (extension || "").replace(/^\./, "").toLowerCase();
  if (!ext && url) ext = url.split(".").pop()?.toLowerCase() || "";
  if (ext.includes("pdf")) return "pdf";
  if (["jpg", "jpeg", "png", "gif", "webp", "bmp"].some(imgExt => ext.includes(imgExt))) return "image";
  return "other";
};
//conver mảng để hiển thị ảnh theo hạng mục
const convertListFileThumbnailToCategories = (listFileThumbnail: Array<any>): FileCategory[] => {
  const domain = env.VITE_BASE_URL;
  const grouped: Record<string, FileCategory> = {};
  listFileThumbnail.forEach((file: any) => {
    const {id, name} = getCategoryInfo(file);
    if (!grouped[id]) grouped[id] = {id, name, files: []};
    const url = getFullUrl(file.url_file, domain);
    grouped[id].files.push({
      type: getFileType(file.extension, url), // Sử dụng includes thay vì ===
      url,
      name: file.ten_file || "",
      id: file.id_file as number | undefined, // chuẩn hóa sang id
    });
  });
  return Object.values(grouped);
};

const ThemHopDongStep3Component = forwardRef<IThemHopDongStep3Ref, ThemHopDongStep3Props>(({}: ThemHopDongStep3Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {
    loading,
    tongSoDongNguoiDuocBaoHiem,
    listNguoiDuocBaoHiem,
    timKiemPhanTrangNguoiDuocBaoHiemParams,
    chiTietNguoiDuocBaoHiem,
    chiTietHopDong,
    getChiTietNguoiDuocBaoHiem,
    setTimKiemPhanTrangNguoiDuocBaoHiemParams,
    getDanhSachFileThumbnailTheoDoiTuong,
    uploadFileTheoDoiTuong,
    deleteFileTheoDoiTuong,
  } = useHopDongConNguoiContext();

  const [categories, setCategories] = useState<FileCategory[]>([]);

  // State lưu selectedCategory và selectedFile dựa trên categories thực tế
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [selectedFile, setSelectedFile] = useState<any>(undefined);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const [formTimKiemNguoiDuocBaoHiem] = Form.useForm();

  const fileInputRef = useRef<HTMLInputElement>(null); // Ref cho input file
  const modalQuanLyFileRef = useRef<any>(null); // Ref cho modal quản lý file cá nhân

  const [phanLoaiPopoverVisible, setPhanLoaiPopoverVisible] = useState(false);

  //khởi tạo dữ liệu();
  useEffect(() => {
    initData();
  }, []);

  const initData = useCallback(() => {
    setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, so_id: chiTietHopDong?.so_id, so_hd: chiTietHopDong?.so_hd, trang: 1, so_dong: 10});
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams, chiTietHopDong]);

  // Khi categories thay đổi, tự động chọn category và file đầu tiên nếu có
  useEffect(() => {
    if (categories.length > 0) {
      setSelectedCategory(categories[0].id);
      setSelectedFile(categories[0].files[0]);
    } else {
      setSelectedCategory(undefined);
      setSelectedFile(undefined);
    }
  }, [categories]);

  //bấm chọn 1 đối tượng
  const handleSelectDoiTuongBaoHiem = useCallback(
    async (record: TableNguoiDuocBaoHiemColumnDataType) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      await getChiTietNguoiDuocBaoHiem(record as ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi);
      const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
        so_id: record?.so_id ? Number(record.so_id) : undefined,
        so_id_dt: record?.so_id_dt ? Number(record.so_id_dt) : undefined,
        nv: NGHIEP_VU_NG,
      });
      const newCategories = convertListFileThumbnailToCategories(listFileThumbnail?.data || []);
      setCategories(newCategories);
    },
    [getChiTietNguoiDuocBaoHiem, getDanhSachFileThumbnailTheoDoiTuong],
  );

  //HÀM XỬ LÝ DANH SÁCH ĐỐI TƯỢNG BẢO HIỂM
  const dataTableNguoiDuocBaoHiem = useMemo<Array<TableNguoiDuocBaoHiemColumnDataType>>(() => {
    try {
      const tableData = listNguoiDuocBaoHiem.map(itemNguoiDuocBaoHiem => {
        return {
          key: itemNguoiDuocBaoHiem.so_id_dt + "",
          ten: itemNguoiDuocBaoHiem.ten + " / " + itemNguoiDuocBaoHiem.so_cmt + " / " + itemNguoiDuocBaoHiem.dthoai,
          ngay_hl: itemNguoiDuocBaoHiem.ngay_hl,
          so_id: itemNguoiDuocBaoHiem.so_id + "",
          so_id_dt: itemNguoiDuocBaoHiem.so_id_dt,
          tong_phi: +(itemNguoiDuocBaoHiem.tong_phi || 0),
        };
      });
      if (tableData.length > 0) handleSelectDoiTuongBaoHiem(tableData[0]);
      const arrEmptyRow: Array<TableNguoiDuocBaoHiemColumnDataType> = fillRowTableEmpty(tableData.length, 10);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableNguoiDuocBaoHiem error", error);
      return [];
    }
  }, [listNguoiDuocBaoHiem]);

  const tongPhiBaoHiem = useMemo(() => {
    // Tính tổng phí bảo hiểm
    return listNguoiDuocBaoHiem.reduce((acc, curr) => {
      if (curr && curr.tong_phi) acc += +curr.tong_phi;
      return acc;
    }, 0);
  }, [listNguoiDuocBaoHiem]);

  const timKiemPhanTrangNguoiDuocBaoHiem = useCallback(() => {
    try {
      const values: {nd_tim: string} = formTimKiemNguoiDuocBaoHiem.getFieldsValue(); //bổ sung type này vào do ma_kh đang bị định dạng theo string //lấy ra values của form
      setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, nd_tim: values.nd_tim});
    } catch (error) {
      console.log("timKiemPhanTrangNguoiDuocBaoHiem error", error);
    }
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams]);

  /* HÀM XỬ LÝ HIỂN THỊ FILE*/
  const getTotalFiles = () => {
    return getAllFiles().length;
  };

  // Tìm hạng mục chứa file đang được chọn
  const getCategoryByFile = (file: any) => {
    return categories.find(cat => cat.files.some(f => f.url === file.url));
  };

  // Lấy danh sách file của hạng mục đang chọn
  const currentCategory = selectedFile ? getCategoryByFile(selectedFile) : categories.find(cat => cat.id === selectedCategory);
  const currentFiles = currentCategory ? currentCategory.files : [];

  const getAllFiles = () => {
    return categories.flatMap(cat => cat.files);
  };

  const getCurrentFileIndex = () => {
    if (!selectedFile) return -1;
    const allFiles = getAllFiles();
    return allFiles.findIndex(file => file.url === selectedFile.url);
  };
  const navigateToPrevious = () => {
    if (!selectedFile || !currentFiles.length) return;
    const currentIndex = currentFiles.findIndex(file => file.url === selectedFile.url);
    const prevIndex = currentIndex - 1;

    if (prevIndex >= 0) {
      setSelectedFile(currentFiles[prevIndex]); // Chuyển đến file trước đó trong cùng hạng mục
    } else {
      // Đã đến đầu hạng mục hiện tại, chuyển sang hạng mục trước đó
      const currentCategoryIndex = categories.findIndex(cat => cat.id === selectedCategory);
      const prevCategoryIndex = currentCategoryIndex === 0 ? categories.length - 1 : currentCategoryIndex - 1;
      const prevCategory = categories[prevCategoryIndex];
      if (prevCategory && prevCategory.files.length > 0) {
        setSelectedCategory(prevCategory.id);
        setSelectedFile(prevCategory.files[prevCategory.files.length - 1]);
      }
    }
  };

  // Hàm điều hướng
  const navigateToNext = () => {
    if (!selectedFile || !currentFiles.length) return;
    const currentIndex = currentFiles.findIndex(file => file.url === selectedFile.url);
    const nextIndex = currentIndex + 1;

    if (nextIndex < currentFiles.length) {
      setSelectedFile(currentFiles[nextIndex]); // Chuyển đến file tiếp theo trong cùng hạng mục
    } else {
      // Đã đến cuối hạng mục hiện tại, chuyển sang hạng mục tiếp theo
      const currentCategoryIndex = categories.findIndex(cat => cat.id === selectedCategory);
      const nextCategoryIndex = (currentCategoryIndex + 1) % categories.length;
      const nextCategory = categories[nextCategoryIndex];

      if (nextCategory && nextCategory.files.length > 0) {
        setSelectedCategory(nextCategory.id);
        setSelectedFile(nextCategory.files[0]);
      }
    }
  };

  /* HÀM XỬ LÝ DANH SÁCH FILE */
  // Hàm xử lý upload file
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const fileList = Array.from(files);
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    fileList.forEach(file => {
      // Kiểm tra loại file
      const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
      if (!allowedTypes.includes(file.type)) {
        invalidFiles.push(`${file.name} - Không hỗ trợ định dạng này`);
        return;
      }

      // Kiểm tra kích thước file (giới hạn 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        invalidFiles.push(`${file.name} - File quá lớn (>10MB)`);
        return;
      }

      validFiles.push(file);
    });

    if (invalidFiles.length > 0) message.error(`Các file không hợp lệ:\n${invalidFiles.join("\n")}`);

    if (validFiles.length > 0) message.success(`Đã thêm ${validFiles.length} file vào danh sách upload`);

    // Reset input để có thể chọn lại cùng file
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  // Hàm mở dialog chọn file
  const handleUploadClick = () => {
    // Mở modal quản lý file cá nhân thay vì modal upload files
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  };

  // Hàm xử lý gắn file vào hợp đồng/đối tượng bảo hiểm
  const handleAttachFiles = async (fileSelected: any[]) => {
    if (!chiTietNguoiDuocBaoHiem || !chiTietHopDong) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng bảo hiểm");
      return;
    }
    try {
      // Sử dụng uploadFileTheoDoiTuong để gắn file vào đối tượng bảo hiểm
      const responseUpload = await uploadFileTheoDoiTuong({
        so_id: chiTietHopDong.so_id,
        so_id_dt: chiTietNguoiDuocBaoHiem.so_id_dt,
        file: fileSelected.map(file => ({id: file.id})),
      } as ReactQuery.IUploadFileTheoDoiTuongXeParams);

      if (responseUpload) {
        modalQuanLyFileRef.current.close();
        // Refresh danh sách file sau khi gắn thành công
        if (chiTietNguoiDuocBaoHiem?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDong.so_id,
            so_id_dt: +chiTietNguoiDuocBaoHiem.so_id_dt,
            nv: NGHIEP_VU_NG,
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail?.data || []);
          setCategories(newCategories);
        }
      }
    } catch (err) {
      console.error("Lỗi khi gắn file:", err);
      message.error("Gắn file thất bại!");
    }
  };

  // Hàm xử lý xóa file
  const handleDeleteFiles = async () => {
    if (!chiTietNguoiDuocBaoHiem || !chiTietHopDong) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng bảo hiểme.");
      return;
    }
    // Lấy danh sách file object từ categories theo selectedFiles (url)
    const filesToDelete = categories.flatMap(cat => cat.files).filter(f => selectedFiles.includes(f.url));
    if (filesToDelete.length === 0) {
      message.warning("Vui lòng chọn file để xóa.");
      return;
    }
    try {
      const responseDeleteFile = await deleteFileTheoDoiTuong({
        so_id: chiTietHopDong.so_id,
        so_id_dt: chiTietNguoiDuocBaoHiem?.so_id_dt,
        file: filesToDelete.map(f => ({id: f.id})),
      } as ReactQuery.IUploadFileTheoDoiTuongXeParams);

      if (responseDeleteFile) {
        // Sau khi xóa thành công, làm mới danh sách file
        if (chiTietNguoiDuocBaoHiem?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDong.so_id,
            so_id_dt: +chiTietNguoiDuocBaoHiem.so_id_dt,
            nv: NGHIEP_VU_NG,
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail?.data || []);
          setCategories(newCategories);
          setSelectedFiles([]);
          setSelectedFile(undefined);
        }
      }
    } catch (err) {
      console.error("Lỗi khi xóa file:", err);
      message.error("Xóa file thất bại!");
    }
  };

  // Hàm xử lý phân loại file
  const handlePhanLoaiFiles = async (ma_hang_muc: string) => {
    if (!chiTietNguoiDuocBaoHiem || !chiTietHopDong) {
      message.error("Chưa chọn hợp đồng hoặc đối tượng bảo hiểm.");
      return;
    }
    const filesToUpdate = categories.flatMap(cat => cat.files).filter(f => selectedFiles.includes(f.url));
    if (filesToUpdate.length === 0) {
      message.warning("Vui lòng chọn file để phân loại.");
      return;
    }
    try {
      const response = await phanLoaiFileTheoHangMucXe({
        so_id: chiTietHopDong.so_id,
        so_id_dt: chiTietNguoiDuocBaoHiem.so_id_dt,
        ma_hang_muc: ma_hang_muc,
        file: filesToUpdate.map(f => ({id: f.id})),
      });
      if (response) {
        // setIsPhanLoaiModalVisible(false);
        // Refresh lại danh sách file
        if (chiTietNguoiDuocBaoHiem?.so_id_dt) {
          const listFileThumbnail = await getDanhSachFileThumbnailTheoDoiTuong({
            so_id: chiTietHopDong.so_id,
            so_id_dt: +chiTietNguoiDuocBaoHiem.so_id_dt,
            nv: NGHIEP_VU_NG,
          });
          const newCategories = convertListFileThumbnailToCategories(listFileThumbnail?.data || []);
          setCategories(newCategories);
          setSelectedFiles([]);
          setSelectedFile(undefined);
        }
      }
    } catch (err) {
      message.error("Phân loại file thất bại!");
    }
  };

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 6) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  //render header table đối tượng
  const renderHeaderTableNguoiDuocBaoHiem = () => {
    return (
      <Form form={formTimKiemNguoiDuocBaoHiem} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={timKiemPhanTrangNguoiDuocBaoHiem}>
        <Row gutter={8} className="items-end">
        {renderFormInputColum({...nd_tim}, 24)}
        </Row>
      </Form>
    );
  };

  // RENDER TABLE NGƯỜI ĐƯỢC BẢO HIỂM BÊN TAY TRÁI MODAL
  const renderDanhSachNguoiDuocBaoHiem = () => {
    return (
      <>
        <Table<TableNguoiDuocBaoHiemColumnDataType>
          className="table-danh-sach-nguoi-duoc-bao-hiem-hop-dong-con-nguoi"
          {...defaultTableProps}
          dataSource={dataTableNguoiDuocBaoHiem} //mảng dữ liệu record được hiển thị
          rowClassName={record => (record.so_id_dt && record.so_id_dt === chiTietNguoiDuocBaoHiem?.so_id_dt ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
          //định nghĩa cột của table
          columns={tableNguoiDuocBaoHiemColumn || []}
          loading={loading} //hiển thị loading khi đang gọi API để loading data
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongNguoiDuocBaoHiem,
            defaultPageSize: 10,
            size: "small",
            showSizeChanger: false,
            showQuickJumper: false,
            // hideOnSinglePage: true, //ẩn pagination khi chỉ có 1 page
            onChange: (page, pageSize) => {
              setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, trang: page, so_dong: pageSize});
            },
          }}
          sticky
          title={renderHeaderTableNguoiDuocBaoHiem}
          onRow={record => {
            return {
              style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button
              onClick: async () => {
                if (record.key.toString().includes("empty")) return;
                // await getChiTietNguoiDuocBaoHiem(record as ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi);
                await handleSelectDoiTuongBaoHiem(record);
              },
            };
          }}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                  <div className="text-center font-medium">Tổng phí bảo hiểm</div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                  <div className="text-right font-medium">
                    <NumericFormat value={tongPhiBaoHiem} displayType="text" thousandSeparator="," decimalSeparator="." decimalScale={2} style={{textAlign: "right"}} />
                  </div>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </>
    );
  };

  return (
    <>
      <Row gutter={16} className="mt-4 h-full min-h-[500px] overflow-hidden">
        <Col span={6} className="flex h-[81%] flex-col overflow-hidden" style={{borderRight: "1px solid #eee"}}>
          <div className="mb-1 px-2 font-semibold">Danh sách đối tượng bảo hiểm</div>
          {renderDanhSachNguoiDuocBaoHiem()}
        </Col>
        <Col span={14} className="flex h-full flex-col overflow-hidden px-4">
          {/* {renderFileSelectedView()} */}
          <FileSelectedView
            selectedFile={selectedFile}
            getCurrentFileIndex={getCurrentFileIndex}
            getTotalFiles={getTotalFiles}
            navigateToPrevious={navigateToPrevious}
            navigateToNext={navigateToNext}
          />
        </Col>
        <Col span={4} style={{borderLeft: "1px solid #eee"}} className="flex h-[81%] flex-col overflow-hidden">
          <ListFileView
            categories={categories}
            selectedFile={selectedFile}
            selectedFiles={selectedFiles}
            selectedCategory={selectedCategory}
            fileInputRef={fileInputRef}
            phanLoaiPopoverVisible={phanLoaiPopoverVisible}
            setSelectedFiles={setSelectedFiles}
            setSelectedCategory={setSelectedCategory}
            setSelectedFile={setSelectedFile}
            getCategoryByFile={getCategoryByFile}
            setPhanLoaiPopoverVisible={setPhanLoaiPopoverVisible}
            handleUploadClick={handleUploadClick}
            handlePhanLoaiFiles={handlePhanLoaiFiles}
            handleDeleteFiles={handleDeleteFiles}
            handleFileUpload={handleFileUpload}
          />
        </Col>
      </Row>
      <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleAttachFiles} />
    </>
  );
});

ThemHopDongStep3Component.displayName = "ThemHopDongStep3Component";
export const ThemHopDongStep3 = memo(ThemHopDongStep3Component, isEqual);
