import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  EyeOutlined,
  FileAddOutlined,
  FileOutlined,
  PrinterOutlined,
  ReloadOutlined,
  StopOutlined,
} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, HeaderModal, Popcomfirm} from "@src/components";
import {Alert, Flex, Form, message, Modal, Space, Steps} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {
  FormTaoMoiHopDong,
  IModalThemHopDongRef,
  IThemHopDongStep1Ref,
  IThemHopDongStep2Ref,
  IThemHopDongStep3Ref,
  IThemHopDongStep4Ref,
  messageAlertByStep,
  ModalThemHopDongProps,
  STEP_THEM_HOP_DONG,
} from "./Constant";
import {ThemHopDongStep1} from "./ThemHopDongStep1";
import {ThemHopDongStep2} from "./ThemHopDongStep2";
import {ThemHopDongStep3} from "./ThemHopDongStep3";
import {useNotiContext} from "@src/providers";
import {ThemHopDongStep4} from "./ThemHopDongStep4";
import {ThemHopDongStep5} from "./ThemHopDongStep5";

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
const ModalThemHopDongConNguoiComponent = forwardRef<IModalThemHopDongRef, ModalThemHopDongProps>(({}: ModalThemHopDongProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataHopDong?: CommonExecute.Execute.IHopDongConNguoi) => {
      setIsOpen(true);
      setDisableSubmit(true);
      if (dataHopDong) setChiTietHopDong(dataHopDong);
    },
    close: () => setIsOpen(false),
  }));

  const {
    timKiemPhanTrangHopDongBaoHiem,
    updateHopDong,
    luuThongTinNguoiDuocBaoHiem,
    timKiemPhanTrangNguoiDuocBaoHiem,
    setChiTietNguoiDuocBaoHiem,
    luuDieuKhoanNguoiDuocBaoHiem,
    luuDieuKhoanBoSungNguoiDuocBaoHiem,
    loading,
    chiTietNguoiDuocBaoHiem,
  } = useHopDongConNguoiContext();

  const {noti} = useNotiContext();

  const refThemHopDongStep1 = useRef<IThemHopDongStep1Ref>(null);
  const refThemHopDongStep2 = useRef<IThemHopDongStep2Ref>(null);
  const refThemHopDongStep3 = useRef<IThemHopDongStep3Ref>(null);
  const refThemHopDongStep4 = useRef<IThemHopDongStep4Ref>(null);

  const [chiTietHopDong, setChiTietHopDong] = useState<CommonExecute.Execute.IHopDongConNguoi | null>(null);

  const [step, setStep] = useState<number>(0);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [formThongTinHopDong] = Form.useForm();
  const [formThongTinNguoiBaoHiem] = Form.useForm();

  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isChangeData, setIsChangeData] = useState<boolean>(false); //CHECK XEM DATA CÓ ĐANG THAY ĐỔI MÀ CHƯA LƯU HAY KHÔNG

  const allFormValuesHopDong = Form.useWatch([], formThongTinHopDong);
  const allFormValuesNguoiBaoHiem = Form.useWatch([], formThongTinNguoiBaoHiem);

  // init form data
  useEffect(() => {
    if (chiTietHopDong) {
      fillValueVaoForm();
    }
  }, [chiTietHopDong]);

  const fillValueVaoForm = useCallback(async () => {
    const arrFormData = [];
    const arrInputFormThongTinHopDong = Object.keys(FormTaoMoiHopDong); //lấy ra key của form
    //chỉ điền những thuộc tính cần hiển thị lên input
    for (const key in chiTietHopDong) {
      if (arrInputFormThongTinHopDong.includes(key)) {
        let value: any = chiTietHopDong[key as keyof CommonExecute.Execute.IHopDongConNguoi];
        //xử lý các key đặc biệt
        if (key === "ngay_cap") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "ngay_hl") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "gio_hl") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
        else if (key === "ngay_kt") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
        else if (key === "gio_kt") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
        else if (key === "vip") value = value == "VIP" ? value : "K";

        arrFormData.push({
          name: key as keyof CommonExecute.Execute.IHopDongConNguoi,
          value: value,
        });
      }
    }
    //đổ data vào form
    formThongTinHopDong.setFields(arrFormData);
    //xử lý riêng input khách hàng
    formThongTinHopDong.setFieldValue("ma_kh", {
      ten: chiTietHopDong?.ten_kh,
      ma: chiTietHopDong?.ma_kh,
      key: chiTietHopDong?.ma_kh,
    });
    //xử lý riêng input cán bộ
    formThongTinHopDong.setFieldValue("ma_cb_ql", {
      ten: chiTietHopDong?.ten_cb_ql,
      ma: chiTietHopDong?.ma_cb_ql,
      key: chiTietHopDong?.ma_cb_ql,
    });

    //xử lý riêng input cán bộ
    formThongTinHopDong.setFieldValue("daily_kt", {
      ten: chiTietHopDong?.ten_daily_kt,
      ma: chiTietHopDong?.daily_kt,
      key: chiTietHopDong?.daily_kt,
    });
  }, [formThongTinHopDong, chiTietHopDong]);

  //xử lý disable nút Lưu khi form chưa valid
  useEffect(() => {
    if (step === 0) {
      formThongTinHopDong
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(response => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(error => {
          //xử lý trường hợp khi back từ step 2 về step 1 -> dính lỗi so_hd_g
          if (allFormValuesHopDong.kieu_hd === "G" && error?.errorFields[0]?.name[0] === "so_hd_g") {
            setDisableSubmit(false); // nếu có lỗi -> cho disable nút Lưu
          } else setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
        });
    } else if (step === 1) {
      formThongTinNguoiBaoHiem
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(response => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(error => {
          if (error.errorFields.length > 0) setDisableSubmit(true);
          else setDisableSubmit(false); // nếu có lỗi -> cho disable nút Lưu
        });
    } else if (step === 2) {
      setDisableSubmit(false);
    }
  }, [
    // form thông tin hợp đồng
    formThongTinHopDong,
    allFormValuesHopDong,
    //form đối tượng bảo hiểm
    formThongTinNguoiBaoHiem,
    allFormValuesNguoiBaoHiem,
    step,
    refThemHopDongStep2,
  ]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietHopDong(null);
    setChiTietNguoiDuocBaoHiem(undefined);
    formThongTinHopDong.resetFields();
    formThongTinNguoiBaoHiem.resetFields();
    setStep(0);
    setIsChangeData(false);
  }, [formThongTinHopDong, formThongTinNguoiBaoHiem]);

  //Bấm nút  ĐÓNG / QUAY LẠI / TIẾP THEO / LƯU / LƯU VÀ ĐÓNG
  const onConfirm = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    try {
      // STEP === 0 -> LƯU THÔNG TIN HỢP ĐỒNG
      if (step === 0) luuThongTinHopDongStep1(action, newStep);
      // STEP === 1 -> LƯU THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM
      else if (step === 1) {
        const currentTab = refThemHopDongStep2.current?.getCurrentTab();
        if (currentTab === "1") luuThongTinNguoiDuocBaoHiemStep2(action, newStep);
        else if (currentTab === "2") luuQuyenLoiChinhStep2(action, newStep);
        else if (currentTab === "3") luuQuyenLoiBoSungStep2(action, newStep);
      }
      setIsChangeData(false);
    } catch (error) {
      console.log("onConfirm error", error);
    }
  };

  // LƯU THÔNG TIN HỢP ĐỒNG
  const luuThongTinHopDongStep1 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const values: ReactQuery.IUpdateHopDongParams & {
      ma_kh: {
        ma: string;
        ten: string;
        key: string;
      };
      ma_cb_ql: {
        ma: string;
        ten: string;
        key: string;
      };
      daily_kt: {
        ma: string;
        ten: string;
        key: string;
      }; //bổ sung type này vào do ma_kh đang bị định dạng theo string
    } = formThongTinHopDong.getFieldsValue(); //lấy ra values của form
    console.log("values", values);

    const params: ReactQuery.IUpdateHopDongParams = {
      ...values,
      so_id: chiTietHopDong ? chiTietHopDong.so_id : undefined,
      nv: "NG",
      ma_kh: values.ma_kh.ma || "",
      ma_cb_ql: values.ma_cb_ql.ma || "",
      daily_kt: values.daily_kt.ma || "",
      ngay_cap: dayjs(values.ngay_cap).format("YYYYMMDD"),
      gio_hl: dayjs(values.gio_hl).format("HH:mm"),
      ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
      gio_kt: dayjs(values.gio_kt).format("HH:mm"),
      ngay_kt: dayjs(values.ngay_kt).format("YYYYMMDD"),
      vip: values.vip === "K" ? "" : values.vip,
    };
    console.log("params", params);
    const response = await updateHopDong(params); //cập nhật lại đơn vị chi nhánh
    if (response.data === -1) {
      await timKiemPhanTrangHopDongBaoHiem(); //lấy lại danh sách đơn vị chi nhánh
      message.success((!chiTietHopDong ? "Tạo mới" : "Cập nhật") + " thành công ");
      if (action === "Lưu và đóng") setIsOpen(false);
      if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(1);
      }
    }
  };

  //LƯU THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM STEP 2
  const luuThongTinNguoiDuocBaoHiemStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const values: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi = formThongTinNguoiBaoHiem.getFieldsValue(); //bổ sung type này vào do ma_kh đang bị định dạng theo string //lấy ra values của form
    console.log("values", values);
    // ĐANG LÀM DỞ Ở ĐÂY

    const params: ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi = {
      so_id: chiTietHopDong?.so_id,
      so_id_dt: chiTietNguoiDuocBaoHiem?.so_id_dt,
      ...values,
      ngay_cap: dayjs(values.ngay_cap).format("YYYYMMDD"),
      gio_hl: dayjs(values.gio_hl).format("HH:mm"),
      ngay_hl: dayjs(values.ngay_hl).format("YYYYMMDD"),
      gio_kt: dayjs(values.gio_kt).format("HH:mm"),
      ngay_kt: dayjs(values.ngay_kt).format("YYYYMMDD"),
      ngay_sinh: dayjs(values.ngay_sinh).format("YYYYMMDD"),
    };

    const response = await luuThongTinNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    if (response.data === -1) {
      await timKiemPhanTrangNguoiDuocBaoHiem();
      message.success((!chiTietNguoiDuocBaoHiem ? "Tạo mới" : "Cập nhật") + " thành công ");
      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
      // else setStep(2);
    }
    console.log("response", response);
  };

  //LƯU QUYỀN LỢI CHÍNH STEP 2
  const luuQuyenLoiChinhStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const listQuyenLoi = refThemHopDongStep2.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    const checboxApDungGoiBaoHiem = refThemHopDongStep2.current?.getCheckboxApDungGoiBaohiemQuyenLoi() || false;
    const params: ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams = {
      so_id: chiTietHopDong ? chiTietHopDong.so_id : 0,
      so_id_dt: chiTietNguoiDuocBaoHiem ? +chiTietNguoiDuocBaoHiem.so_id_dt : 0,
      ad_goi_bh: checboxApDungGoiBaoHiem ? "C" : "K",
      dk: [], //QUYỀN LỢI
    };
    //QUYỀN LỢI BẢO HIỂM
    for (let i = 0; i < listQuyenLoi.length; i++) {
      const itemQuyenLoi = listQuyenLoi[i];
      params.dk?.push({...itemQuyenLoi});
    }
    console.log("paramsCapNhat", params);
    const response = await luuDieuKhoanNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    console.log("response", response);
    if (response === -1) {
      message.success("Cập nhật thành công");
      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
    }
  };

  //LƯU QUYỀN LỢI BỔ SUNG STEP 2
  const luuQuyenLoiBoSungStep2 = async (action: "Lưu" | "Lưu và đóng" | "Lưu và chuyển tiếp" | "Lưu và quay lại" | "", newStep?: number) => {
    const listQuyenLoi = refThemHopDongStep3.current?.getListQuyenLoiNguoiDuocBaoHiem() || [];
    const checboxApDungGoiBaoHiem = refThemHopDongStep2.current?.getCheckboxApDungGoiBaohiemQuyenLoiBoSung() || false;
    const params: ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams = {
      so_id: chiTietHopDong ? chiTietHopDong.so_id : 0,
      so_id_dt: chiTietNguoiDuocBaoHiem ? +chiTietNguoiDuocBaoHiem.so_id_dt : 0,
      ad_goi_bh: checboxApDungGoiBaoHiem ? "C" : "K",
      dk: [], //QUYỀN LỢI
    };
    //QUYỀN LỢI BẢO HIỂM
    for (let i = 0; i < listQuyenLoi.length; i++) {
      const itemQuyenLoi = listQuyenLoi[i];
      params.dk?.push({...itemQuyenLoi});
    }
    console.log("paramsCapNhat", params);
    const response = await luuDieuKhoanBoSungNguoiDuocBaoHiem(params); //cập nhật lại đơn vị chi nhánh
    if (response === -1) {
      message.success("Cập nhật thành công");
      if (action === "Lưu và đóng") setIsOpen(false);
      else if (action === "Lưu và chuyển tiếp") {
        if (newStep !== undefined) setStep(newStep);
        else setStep(2);
      } else if (action === "Lưu và quay lại") setStep(0);
    }
  };

  //LẤY TITLE CỦA BUTTON BACK THEO STEP
  const getTitleButtonBackByStep = useCallback((current: number) => {
    if (current === 0) return "Đóng";
    return "Quay lại";
  }, []);

  const getOkTextBtnBackByStep = useCallback(
    (current: number) => {
      if (current === 0) return "Lưu và đóng";
      return "Lưu và quay lại";
    },
    [refThemHopDongStep2],
  );

  const getCancelTextBtnBackByStep = useCallback(
    (current: number) => {
      if (current === 0) return "Đóng";
      return "Quay lại";
    },
    [refThemHopDongStep2],
  );

  const getDescriptionDongVaTiepTheoByStep = (current: number) => {
    if (current === 0) return "Bạn chưa lưu Thông tin hợp đồng!";
    else if (current === 1) {
      if (refThemHopDongStep2.current?.getCurrentTab() === "1") return "Bạn chưa lưu Thông tin đối tượng bảo hiểm!";
      else if (refThemHopDongStep2.current?.getCurrentTab() === "2") return "Bạn chưa lưu Quyền lợi chính!";
    }
  };

  //LẤY TITLE CỦA BUTTON BACK THEO STEP
  const getIconButtonBackByCurrentStep = useCallback((current: number) => {
    if (current == 0) return <CloseOutlined />;
    return <ArrowLeftOutlined />;
  }, []);

  const onBack = () => {
    if (step === 0) return closeModal();
    setStep(step - 1);
  };

  const onClickBtnTiepTheo = () => {
    if (step === 4) return;
    setStep(step + 1);
  };

  const onChangeStep = (newStep: number) => {
    if (isChangeData) {
      const btn = (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setStep(newStep);
              noti.destroy();
            }}>
            Chuyển bước {newStep + 1}
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              await onConfirm(newStep > step ? "Lưu và chuyển tiếp" : "Lưu và quay lại", newStep);
              noti.destroy();
            }}>
            Lưu và chuyển bước {newStep + 1}
          </Button>
        </Space>
      );
      noti.warning({
        message: "Thông báo",
        description: getDescriptionDongVaTiepTheoByStep(step),
        btn,
        placement: "topRight",
      });
    } else setStep(newStep);
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    const showButtonHuy = Number(chiTietHopDong?.ngay_huy_num) >= 30000101;
    const showButtonGoHuy = Number(chiTietHopDong?.ngay_huy_num) < 30000101;
    // const showButtonHuyTrinh = Number(chiTietHopDong?.ngay_trinh_num) < 30000101;
    return (
      <div className="flex w-full items-center justify-between">
        <div>
          {step === 0 && (
            <div>
              <Button type="primary" className="mr-2" icon={<FileOutlined />}>
                Tạo SĐBS
              </Button>
              <Button type="primary" className="mr-2" icon={<PrinterOutlined />}>
                Xem hợp đồng
              </Button>

              {/* BUTTON HUỶ HỢP ĐỒNG XE */}
              {showButtonHuy && (
                <Popcomfirm
                  danger
                  variant="outlined"
                  title="Thông báo"
                  // onConfirm={huyHopDongXe}
                  okText="Đồng ý"
                  description="Bạn có chắc chắn muốn huỷ hợp đồng này hay không?"
                  buttonTitle="Huỷ hợp đồng"
                  buttonClassName="mr-2"
                  buttonIcon={<StopOutlined />}
                  type="default"
                />
              )}
              {/* BUTTON GỠ HUỶ HĐ */}
              {showButtonGoHuy && (
                <Popcomfirm
                  title="Thông báo"
                  // onConfirm={goHuyHopDongXe}
                  okText="Đồng ý"
                  description="Bạn có chắc chắn muốn gỡ huỷ hợp đồng này hay không?"
                  buttonTitle="Gỡ huỷ hợp đồng"
                  buttonClassName="mr-2"
                  buttonIcon={<ReloadOutlined />}
                  type="default"
                />
              )}
              {/* BUTTON COPY HĐ */}
              <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                Copy hợp đồng
              </Button>
            </div>
          )}

          {/* BUTTON MỞ MODAL TRÌNH DUYỆT */}
          {/* {step === 4 && !showButtonHuyTrinh && (
            <Button disabled={!chiTietHopDong} type="primary" onClick={() => refModalTrinhDuyetHopDongXe?.current?.open()} className="mr-2" icon={<EditOutlined />}>
              Trình duyệt
            </Button>
          )} */}

          {/* BUTTON HUỶ TRÌNH */}
          {/* {step === 4 && showButtonHuyTrinh && (
            <Popcomfirm
              danger
              title="Thông báo"
              onConfirm={() => {
                // huyTrinhPheDuyetHopDong();
              }}
              okText="Đồng ý"
              description="Bạn có chắc chắn muốn huỷ trình hợp đồng này hay không?"
              buttonTitle="Huỷ trình"
              buttonClassName="mr-2"
              buttonIcon={<CloseOutlined />}
              type="default"
            />
          )} */}
          {/* CÁC BUTTON CHƯA LÀM ACTION */}
          {step === 1 && (
            <>
              <Button type="primary" className="mr-2" icon={<CopyOutlined />}>
                Copy đối tượng
              </Button>
              <Button type="primary" className="mr-2" icon={<EyeOutlined />}>
                Xem GCN
              </Button>
              <Button danger type="default" className="mr-2" icon={<CloseOutlined />}>
                Xoá đối tượng
              </Button>
            </>
          )}
        </div>
        {/* Bên phải: các nút còn lại */}
        <div>
          {/* {step === 2 && (
            <Button
              type="primary"
              onClick={() => refModalDanhGiaTonThat?.current?.open({so_id: chiTietHopDongBaoHiemXe?.so_id, so_id_dt: chiTietDoiTuongBaoHiemXe?.gcn?.so_id_dt || 0})}
              className="mr-2"
              icon={<FileAddOutlined />}
              loading={loading}
              disabled={disableSubmit}>
              Đánh giá rủi do
            </Button>
          )} */}
          {isChangeData ? (
            <Popcomfirm
              title="Thông báo"
              onConfirm={() => onConfirm(step === 0 ? "Lưu và đóng" : "Lưu và quay lại")}
              onCancel={onBack}
              okText={getOkTextBtnBackByStep(step)}
              cancelText={getCancelTextBtnBackByStep(step)}
              description={getDescriptionDongVaTiepTheoByStep(step)}
              buttonTitle={getTitleButtonBackByStep(step)}
              buttonDisable={disableSubmit}
              buttonClassName="ml-2"
              buttonIcon={getIconButtonBackByCurrentStep(step)}
              loading={loading}
              buttonType={"default"}
            />
          ) : (
            <Button type="default" onClick={onBack} icon={getIconButtonBackByCurrentStep(step)}>
              {getTitleButtonBackByStep(step)}
            </Button>
          )}

          {isChangeData ? (
            <Popcomfirm
              title="Thông báo"
              onConfirm={() => onConfirm("Lưu và chuyển tiếp")}
              onCancel={onClickBtnTiepTheo}
              okText="Lưu và chuyển tiếp"
              cancelText="Bỏ qua không lưu"
              description={getDescriptionDongVaTiepTheoByStep(step)}
              buttonTitle="Tiếp theo"
              buttonDisable={disableSubmit}
              buttonClassName="ml-2"
              buttonIcon={<ArrowRightOutlined />}
              loading={loading}
              buttonIconPosition="end"
            />
          ) : (
            <Button disabled={!chiTietHopDong} type="primary" onClick={onClickBtnTiepTheo} className="ml-2" iconPosition="end" icon={<ArrowRightOutlined />}>
              Tiếp theo
            </Button>
          )}
          {/* <Button
            disabled={!chiTietHopDong}
            type="primary"
            onClick={() => {
              onClickBtnTiepTheo();
            }}
            className="ml-2"
            iconPosition="end"
            icon={<ArrowRightOutlined />}>
            Tiếp theo
          </Button> */}
          {(step === 0 || step === 1) && (
            <>
              <Button type="primary" onClick={() => onConfirm("Lưu")} className="ml-2" icon={<CheckOutlined />} loading={loading} disabled={disableSubmit}>
                Lưu
              </Button>
              <Popcomfirm
                title="Thông báo"
                onConfirm={() => onConfirm("Lưu và đóng")}
                okText="Lưu"
                description={messageAlertByStep[step]}
                buttonTitle="Lưu và đóng"
                buttonDisable={disableSubmit}
                buttonClassName="ml-2"
                buttonIcon={<CheckOutlined />}
                loading={loading}
              />
            </>
          )}
        </div>
      </div>
    );
  };

  //Render
  return (
    <Flex vertical gap="middle" align="center">
      <Modal
        title={
          <HeaderModal
            title={chiTietHopDong ? `Chi tiết hợp đồng ${chiTietHopDong.so_hd}` : "Tạo mới hợp đồng"}
            trang_thai_ten={chiTietHopDong?.trang_thai_ten}
            trang_thai={chiTietHopDong?.trang_thai}
          />
        }
        destroyOnClose={true}
        className="modal-them-hop-dong-con-nguoi"
        maskClosable={false}
        centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="100vw"
        styles={{
          body: {
            height: "80vh",
          },
        }}
        footer={renderFooter}>
        <Steps current={step} items={STEP_THEM_HOP_DONG} percent={(step + 1) * 20} onChange={onChangeStep} size="small" />
        {step === 0 && <ThemHopDongStep1 ref={refThemHopDongStep1} formThongTinHopDong={formThongTinHopDong} setIsChangeData={setIsChangeData} />}
        {step === 1 && <ThemHopDongStep2 ref={refThemHopDongStep2} formThongTinNguoiBaoHiem={formThongTinNguoiBaoHiem} setIsChangeData={setIsChangeData} />}
        {step === 2 && <ThemHopDongStep3 ref={refThemHopDongStep3} />}
        {step === 3 && <ThemHopDongStep4 ref={refThemHopDongStep4} />}
        {step === 4 && <ThemHopDongStep5 ref={refThemHopDongStep4} />}
      </Modal>
    </Flex>
  );
});

ModalThemHopDongConNguoiComponent.displayName = "ModalThemHopDongConNguoiComponent";
export const ModalThemHopDongConNguoi = memo(ModalThemHopDongConNguoiComponent, isEqual);
