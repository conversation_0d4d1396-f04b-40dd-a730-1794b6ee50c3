import {ReactQuery} from "@src/@types";

export interface DanhMucSanPhamContextProps {
  loading: boolean;
  tongSoDong: number;
  // defaultFormValue: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams;
  filterParams: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang>>;
  onUpdateDanhMucSanPham: (item: ReactQuery.IUpdateDanhMucSanPhamParams) => Promise<number | null | undefined>;
  layDanhSachSanPhamPhanTrang: (params: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams) => void;
  danhSachSanPhamPhanTrang: Array<CommonExecute.Execute.IDanhMucSanPham>;
  layChiTietSanPham: (params: ReactQuery.IChiTietDanhMucSanPhamParams) => Promise<CommonExecute.Execute.IDanhMucSanPham | null>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  getListDoiTac: () => Promise<void>;
}
