import {ReactQuery} from "@src/@types";

export interface IXayDungGoiBaoHiemContextProps {
  windowHeight: number;

  loading: boolean;
  filterGoiBaoHiemParams: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams;
  tongSoDongGoiBaoHiem: number;
  listGoiBaoHiem: Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>;
  listQuyenLoiRoot: CommonExecute.Execute.IDanhSachBoMaQuyenLoi[];

  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listNguyenTe: Array<CommonExecute.Execute.IChiTietBoMaNguyenTe>;

  timKiemPhanTrangGoiBaoHiem: () => Promise<any>;
  setFilterGoiBaoHiemParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams>>;
  getChiTietGoiBaoHiem: (params: ReactQuery.IChiTietGoiBaoHiemParams) => Promise<CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi>;
  timKiemPhanTrangBoMaQuyenLoi: (params: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams) => Promise<void>;
  updateGoiBaoHiem: (params: ReactQuery.ICapNhatGoiBaoHiemParams) => Promise<any>;
}
