import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState, useContext} from "react";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {IFormInput, ReactQuery} from "@src/@types";

import DanhMucKhuVucContext from "../index.context";
import {FormTaoMoiKhuVuc, TRANG_THAI_TAO_MOI_KHU_VUC} from "../index.configs";
import {IModalChiTietKhuVucRef} from "./index.configs";

interface IModalChiTietKhuVucProps {
  onAfterSave?: () => void;
  danhSachChauLuc?: Array<{ma: string; ten: string}>; // Danh sách tỉnh thành cho dropdown
}

const ModalChiTietKhuVucComponent = forwardRef<IModalChiTietKhuVucRef, IModalChiTietKhuVucProps>(
  ({onAfterSave, danhSachChauLuc = []}, ref) => {
    // ===== CONTEXT & FORM =====
    const {capNhatChiTietKhuVuc, listKhuVuc, loading} = useContext(DanhMucKhuVucContext);
    const [form] = Form.useForm();

    // ===== STATE =====
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [chiTietKhuVuc, setChiTietKhuVuc] = useState<CommonExecute.Execute.IDanhMucKhuVuc | null>(null);
    const [disableSubmit, setDisableSubmit] = useState<boolean>(true);

    // ===== FORM VALUES WATCHING =====
    const formValues = Form.useWatch([], form);

    // ===== FORM CONFIGURATIONS =====
    const {ma_chau_luc, ma, ten, stt, trang_thai} = FormTaoMoiKhuVuc;

    // ===== DROPDOWN OPTIONS =====
    const ChauLucOptions = danhSachChauLuc.filter(item => item.ma !== ""); // Bỏ "Tất cả" option
    const trangThaiOptions = TRANG_THAI_TAO_MOI_KHU_VUC;
    
    // Debug log để kiểm tra trangThaiOptions
    console.log("[ModalChiTietKhuVuc] trangThaiOptions:", trangThaiOptions);

    // ===== IMPERATIVE HANDLE =====
    useImperativeHandle(ref, () => ({
      open: (data?: CommonExecute.Execute.IDanhMucKhuVuc) => {
        setChiTietKhuVuc(data || null);
        setIsOpen(true);
      },
    }));

    // ===== EFFECTS =====

    // Set form values when modal opens
    useEffect(() => {
      if (chiTietKhuVuc && isOpen) {
        // Chỉnh sửa - load data vào form
        const arrFormData = [];
        for (const key in chiTietKhuVuc) {
          let value = chiTietKhuVuc[key as keyof CommonExecute.Execute.IDanhMucKhuVuc];
          arrFormData.push({
            name: key,
            value: value,
          });
        }
        form.setFields(arrFormData);
      } else if (!chiTietKhuVuc && isOpen) {
        // Tạo mới - set giá trị mặc định
        form.setFields([
          {name: "ma_chau_luc", value: undefined},
          {name: "ma", value: ""},
          {name: "ten", value: ""},
          {name: "stt", value: ""},
          {name: "trang_thai", value: "D"}, // Mặc định là đang sử dụng
        ]);
      }
    }, [chiTietKhuVuc, isOpen, form]);

    // Form validation
    useEffect(() => {
      if (!isOpen) return;

      form
        .validateFields({validateOnly: true})
        .then(() => {
          setDisableSubmit(false);
        })
        .catch(() => {
          setDisableSubmit(true);
        });
    }, [form, formValues, isOpen]);

    // ===== HANDLERS =====

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietKhuVuc(null);
      form.resetFields();
    }, [form]);

    const onConfirm = async () => {
      try {
        const values: ReactQuery.ICapNhatDanhMucKhuVucParams = form.getFieldsValue();

        // Validate mã khu vực không trùng khi tạo mới (giống như DanhMucChauLuc)
        if (!chiTietKhuVuc) {
          for (let i = 0; i < listKhuVuc.length; i++) {
            if (listKhuVuc[i].ma === values.ma) {
              form.setFields([
                {
                  name: "ma",
                  errors: ["Mã khu vực đã tồn tại trong châu lục này!"],
                },
              ]);
              return;
            }
          }
        }

        await capNhatChiTietKhuVuc(values);
        closeModal();
        onAfterSave?.();
      } catch (error) {
        console.log("onConfirm error", error);
      }
    };

    // ===== RENDER FUNCTIONS =====

    const renderFooter = () => {
      return (
        <Form.Item>
          <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
            Quay lại
          </Button>
          <Popcomfirm
            title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
            onConfirm={onConfirm}
            okText="Lưu"
            description="Bạn có chắc muốn lưu thông tin?"
            buttonTitle={"Lưu"}
            buttonDisable={disableSubmit}
            buttonClassName="w-40"
            buttonIcon={<CheckOutlined />}
            iconPosition="end"
            loading={loading}
          />
        </Form.Item>
      );
    };
    const renderFormColumn = (props: IFormInput, span = 8) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
   

    const renderForm = () => (
      <Form form={form} layout="vertical">
        {/* TỈNH THÀNH, NGÀY ÁP DỤNG, MÃ QUẬN HUYỆN */}
        <Row gutter={16}>
          {renderFormColumn({...ma, disabled: chiTietKhuVuc ? true : false},8)}
          {renderFormColumn({...ten},16)}
        </Row>

        {/* TÊN QUẬN HUYỆN, MÃ BƯU ĐIỆN, STT */}
        <Row gutter={16}>
          {renderFormColumn({...ma_chau_luc, options: ChauLucOptions,disabled: chiTietKhuVuc ? true : false},8)}
          {renderFormColumn({...stt},8)}
          {renderFormColumn({
            ...trang_thai, 
            options: trangThaiOptions,
            fieldNames: {label: "label", value: "value"}
          },8)}
        </Row>

        {/* TRẠNG THÁI */}
        <Row gutter={16}>
         
        </Row>
      </Form>
    );

    // ===== MAIN RENDER =====
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={
            <HeaderModal
              title={chiTietKhuVuc ? `${chiTietKhuVuc.ten}` : "Tạo mới quận huyện"}
              trang_thai_ten={chiTietKhuVuc?.trang_thai_ten}
              trang_thai={chiTietKhuVuc?.trang_thai}
            />
          }
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={650}
          styles={{
            body: {
              paddingTop: "8px",
              paddingBottom: "16px",
            },
          }}
          footer={renderFooter()}
        >
          {renderForm()}
        </Modal>
      </Flex>
    );
  }
);

ModalChiTietKhuVucComponent.displayName = "ModalChiTietKhuVucComponent";
export const ModalChiTietKhuVuc = memo(ModalChiTietKhuVucComponent, isEqual);
