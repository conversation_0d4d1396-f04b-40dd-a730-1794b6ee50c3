import {useContext, createContext} from "react";
import {BoMaQuyenLoiContextProps} from "./index.model";
export const BoMaQuyenLoiContext = createContext<BoMaQuyenLoiContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachBoMaQuyenLoi: [],
  danhSachBoMaQuyenLoiCha: [],
  loading: false,
  tongSoDong: 0,
  tongSoDongQLCha: 0,
  listSanPham: [],
  listDoiTac: [],
  filterParams: {},
  setFilterParams: () => {},
  layDanhSachBoMaQuyenLoi: () => Promise.resolve(null),
  layDanhSachBoMaQuyenLoiCha: () => Promise.resolve(null),
  layChiTietBoMaQuyenLoi: () => Promise.resolve(null),
  updateBoMaQuyenLoi: () => Promise.resolve(null),
  getListSanPhamTheoDoiTac: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
});
export const useBoMaQuyenLoiContext = () => useContext(BoMaQuyenLoiContext);
