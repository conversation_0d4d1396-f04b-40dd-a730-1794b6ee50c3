import {ReactQuery} from "@src/@types";

export interface ChuongTrinhBaoHiemContextProps {
  loading: boolean;
  tongSoDong: number;
  listGoiBaoHiem: Array<CommonExecute.Execute.IGoiBaoHiem>;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  danhSachChuongTrinhBaoHiemPhanTrang: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  danhSachGoiBaoHiemPhanTrang: Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>;
  listSanPham: Array<CommonExecute.Execute.IDanhMucSanPham>;
  filterParams: ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams & ReactQuery.IPhanTrang>>;
  onUpdateChuongTrinhBaoHiem: (item: ReactQuery.ICapNhatChuongTrinhBaoHiemParams) => Promise<number | null | undefined>;
  layDanhSachChuongTrinhBaoHiemPhanTrang: (params: ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams) => void;
  layChiTietChuongTrinhBaoHiem: (params: ReactQuery.IChiTietChuongTrinhBaoHiemParams) => Promise<CommonExecute.Execute.IChuongTrinhBaoHiem | null>;
  getListDoiTac: () => Promise<void>;
  getListSanPhamTheoDoiTacNV: (params: ReactQuery.ILietKeSanPhamParams) => void;
  getListGoiBaoHiemTheoCTBH: (params: ReactQuery.ILietKeGoiBaoHiemParams) => void;
  onXoaGoiBaoHiemKhoiCTBH: (item: ReactQuery.IXoaGoiBaoHiemKhoiCTBHParams) => Promise<number | null | undefined>;
  layDanhSachGoiBaoHiemPhanTrang: (params: ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams) => void;
  onUpdateGoiTrongCTBH: (item: ReactQuery.ICapNhatGoiCTBHParams) => Promise<number | null | undefined>;
}
