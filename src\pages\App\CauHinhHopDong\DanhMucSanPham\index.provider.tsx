import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import React, {PropsWithChildren, useEffect, useMemo, useCallback} from "react";
import {useState} from "react";
import {DanhMucSanPhamContextProps} from "./index.model";
import {DanhMucSanPhamContext} from "./index.context";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
// import {defaultFormValue} from "./index.configs";
import {message} from "antd";

const DanhMucSanPhamProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  console.log("Danh mục sản phẩm PROVIDER", children);
  const mutateUseCommonExecute = useCommonExecute();
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSachSanPhamPhanTrang, setDanhSachSanPham] = useState<Array<CommonExecute.Execute.IDanhMucSanPham>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang>({
    ma_doi_tac_ql: "",
    ma: "",
    ten: "",
    nv: "",
    trang_thai: "",
    // trang: 1,
    // so_dong: 20,
  });
  useEffect(() => {
    initData();
  }, []);
  const initData = () => {
    layDanhSachSanPhamPhanTrang(filterParams);
    getListDoiTac();
  };
  useEffect(() => {
    layDanhSachSanPhamPhanTrang(filterParams);
  }, [filterParams]);
  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      setListDoiTac(response?.data?.map(item => ({...item, ten: item.ma + " - " + item.ten})));
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  const layDanhSachSanPhamPhanTrang = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_SAN_PHAM,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        console.log("data", data);
        setDanhSachSanPham(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách sản phẩm error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy chi tiết 1 sản phẩm
  const layChiTietSanPham = useCallback(
    async (item: ReactQuery.IChiTietDanhMucSanPhamParams): Promise<CommonExecute.Execute.IDanhMucSanPham | null> => {
      try {
        const params = {
          ma: item.ma,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          nv: item.nv,
          actionCode: ACTION_CODE.GET_CHI_TIET_SAN_PHAM,
        };
        console.log("params", params);
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        console.log("responseData", responseData);
        return responseData.data as CommonExecute.Execute.IDanhMucSanPham;
      } catch (error: any) {
        console.log("layChiTietSanPham error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  const onUpdateDanhMucSanPham = useCallback(
    async (body: ReactQuery.IUpdateDanhMucSanPhamParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_CAP_NHAT_SAN_PHAM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if (responseData && (responseData.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin thành công!");
          initData();
          return responseData.data as unknown as number;
        }
      } catch (error: any) {
        console.log("onUpdateDanhMucSanPham error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );
  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<DanhMucSanPhamContextProps>(
    () => ({
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      // defaultFormValue,
      filterParams,
      danhSachSanPhamPhanTrang,
      listDoiTac,
      setFilterParams,
      layDanhSachSanPhamPhanTrang,
      layChiTietSanPham,
      onUpdateDanhMucSanPham,
      getListDoiTac,
    }),
    [danhSachSanPhamPhanTrang, tongSoDong, mutateUseCommonExecute, filterParams, setFilterParams, layDanhSachSanPhamPhanTrang, layChiTietSanPham, onUpdateDanhMucSanPham, listDoiTac, getListDoiTac],
  );

  return <DanhMucSanPhamContext.Provider value={value}>{children}</DanhMucSanPhamContext.Provider>;
};
export default DanhMucSanPhamProvider;
