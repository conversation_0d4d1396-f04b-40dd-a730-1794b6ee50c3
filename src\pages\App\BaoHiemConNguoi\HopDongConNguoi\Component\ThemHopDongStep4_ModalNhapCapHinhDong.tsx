import {CheckOutlined, CloseOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {formCauHinhDongInputConfigs, IModalCauHinhDongBHRef, listKieuDong, listLoaiDong} from "./Constant";
import {useHopDongConNguoiContext} from "../index.context";

const {ma_dvi_dong, loai_dong, kieu_dong, tl_dong, so_tham_chieu} = formCauHinhDongInputConfigs;

const ModalNhapCauHinhDongComponent = forwardRef<IModalCauHinhDongBHRef, {disabled?: boolean}>(({disabled = false}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChiTietDongBH?: CommonExecute.Execute.IDongBaoHiem) => {
      setIsOpen(true);
      if (dataChiTietDongBH) {
        initFormInputData(dataChiTietDongBH ?? ({} as CommonExecute.Execute.IDongBaoHiem));
      }
    },
    close: () => setIsOpen(false),
  }));

  const {loading, chiTietHopDong, updateCauHinhDongBaoHiem, listDonViDongTai, listChiNhanh} = useHopDongConNguoiContext();

  const [formNhapCauHinhDongBH] = Form.useForm();
  const formValues = Form.useWatch([], formNhapCauHinhDongBH);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const formLoaiDong = Form.useWatch("loai_dong", formNhapCauHinhDongBH);

  //filter đơn vị đồng
  const filteredListDonViDongBH = useMemo(() => {
    if (formLoaiDong === "T" && chiTietHopDong) {
      return listChiNhanh.filter((item: any) => item.ma_doi_tac === chiTietHopDong.ma_doi_tac);
    }
    return listDonViDongTai;
  }, [formLoaiDong, listDonViDongTai, chiTietHopDong]);

  // init modal data
  const initFormInputData = (chiTiet: CommonExecute.Execute.IDongBaoHiem) => {
    const arrFormData = [];
    if (chiTiet) {
      for (const key in chiTiet) {
        const value: any = chiTiet[key as keyof CommonExecute.Execute.IDongBaoHiem];
        arrFormData.push({
          name: key,
          value: value,
        });
      }
    }
    formNhapCauHinhDongBH.setFields(arrFormData);
  };

  //xử lý validate form
  useEffect(() => {
    formNhapCauHinhDongBH
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formNhapCauHinhDongBH, formValues]);

  //Bấm Update
  const onPressUpdateCauHinhDongBH = async () => {
    try {
      const values: ReactQuery.IUpdateCauHinhDongBHParams = formNhapCauHinhDongBH.getFieldsValue(); //lấy ra values của form
      const cleanedValues = {
        ...values,
        so_id: chiTietHopDong?.so_id ?? 0,
      };
      const response = await updateCauHinhDongBaoHiem(cleanedValues);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onPressUpdateCauHinhDongBH", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    formNhapCauHinhDongBH.resetFields();
  };

  const renderFormInputColum = (props?: any, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formNhapCauHinhDongBH" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdateCauHinhDongBH}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin cấu hình đồng BH không?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit || disabled}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="custom-full-modal"
        title={<HeaderModal title="Thông tin đồng bảo hiểm" />}
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"40vw"}
        maskClosable={false}
        footer={renderFooter}>
        <Form id="formNhapCauHinhDongBH" onFinish={onPressUpdateCauHinhDongBH} form={formNhapCauHinhDongBH} layout="vertical">
          <Row gutter={16} className="mt-4">
            {renderFormInputColum({
              ...loai_dong,
              options: listLoaiDong,
              onChange: (value: any) => {
                formNhapCauHinhDongBH.setFieldsValue({ma_dvi_dong: undefined});
              },
            })}
            {renderFormInputColum({...ma_dvi_dong, options: filteredListDonViDongBH})}
            {renderFormInputColum({...kieu_dong, options: listKieuDong})}
            {renderFormInputColum({...tl_dong})}
            {renderFormInputColum({...so_tham_chieu})}
          </Row>
        </Form>
      </Modal>
    </Flex>
  );
});
ModalNhapCauHinhDongComponent.displayName = "ModalNhapCauHinhDongComponent";
export const ModalNhapCauHinhDong = memo(ModalNhapCauHinhDongComponent, isEqual);
