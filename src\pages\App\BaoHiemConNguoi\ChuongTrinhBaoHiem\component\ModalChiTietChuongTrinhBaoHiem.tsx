import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row, Tabs} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {useChuongTrinhBaoHiemContext} from "../index.context";
import FormChiTietChuongTrinhBaoHiem, {
  // goiBaoHiemColumns,
  IModalChiTietChuongTrinhBaoHiemRef,
  NGHIEP_VU_CTBH,
  Props,
  TRANG_THAI_CHI_TIET_CTBH,
} from "./index.configs";
import TabThongTinGoiBaoHiem from "./TabThongTinGoiBaoHiem";

const {ma_doi_tac_ql, ma, ten, nv, stt, trang_thai, ngay_ad, mo_ta, ma_sp} = FormChiTietChuongTrinhBaoHiem;

const ModalChiTietChuongTrinhBaoHiemComponent = forwardRef<IModalChiTietChuongTrinhBaoHiemRef, Props>(({listDoiTac}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChuongTrinhBaoHiem?: CommonExecute.Execute.IChuongTrinhBaoHiem) => {
      setIsOpen(true);
      if (dataChuongTrinhBaoHiem) setChiTietChuongTrinhBaoHiem(dataChuongTrinhBaoHiem);
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietChuongTrinhBaoHiem, setChiTietChuongTrinhBaoHiem] = useState<CommonExecute.Execute.IChuongTrinhBaoHiem | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {loading, onUpdateChuongTrinhBaoHiem, getListGoiBaoHiemTheoCTBH, listSanPham, getListSanPhamTheoDoiTacNV, setFilterParams, filterParams} = useChuongTrinhBaoHiemContext();
  const [formChiTietCTBH] = Form.useForm();
  const formValues = Form.useWatch([], formChiTietCTBH);
  const [filterValues, setFilterValues] = useState<{ma?: string; ma_doi_tac_ql?: string; nv?: string}>({});
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState("1");
  const watchDoiTac = Form.useWatch("ma_doi_tac_ql", formChiTietCTBH);
  const watchNV = Form.useWatch("nv", formChiTietCTBH);
  const watchMaSP = Form.useWatch("ma_sp", formChiTietCTBH);
  useEffect(() => {
    if (chiTietChuongTrinhBaoHiem) {
      // console.log("listGoiBaoHiem", listGoiBaoHiem);
      const arrFormData = [];
      const arrInputFormThongTinCTBH = Object.keys(FormChiTietChuongTrinhBaoHiem);
      for (const key in chiTietChuongTrinhBaoHiem) {
        if (arrInputFormThongTinCTBH.includes(key)) {
          let value: any = chiTietChuongTrinhBaoHiem[key as keyof CommonExecute.Execute.IChuongTrinhBaoHiem];
          if (key === "ngay_ad") {
            value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          }
          arrFormData.push({
            name: key as keyof CommonExecute.Execute.IChuongTrinhBaoHiem,
            value,
          });
        }
      }
      formChiTietCTBH.setFields(arrFormData);
    }
  }, [chiTietChuongTrinhBaoHiem, formChiTietCTBH]);

  useEffect(() => {
    formChiTietCTBH
      .validateFields({validateOnly: true})
      .then(() => {
        setDisableSubmit(false);
      })
      .catch(() => {
        setDisableSubmit(true);
      });
  }, [formChiTietCTBH, formValues]);
  useEffect(() => {
    if (isOpen) {
      setActiveTab("1");
    }
  }, [isOpen]);
  useEffect(() => {
    if (watchMaSP && watchNV) {
      const sanPham = listSanPham.find(sp => sp.ma === (watchMaSP?.value || watchMaSP));
      // setListSanPham();
      if (sanPham && sanPham.nv && sanPham.nv !== watchNV) {
        formChiTietCTBH.setFields([
          {
            name: "nv",
            errors: ["Nghiệp vụ không trùng với nghiệp vụ của sản phẩm đã chọn!"],
          },
        ]);
      }
    }
    if (!watchDoiTac || !watchNV) {
      formChiTietCTBH.setFieldValue("ma_sp", undefined);
    }
  }, [watchMaSP, watchNV, watchDoiTac]);
  const listSanPhamSelect = useMemo(() => {
    if (!watchDoiTac || !watchNV) return []; // nếu chưa chọn đối tác -> không hiển thị gì
    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTac && item.nv === watchNV);
  }, [listSanPham, watchDoiTac, watchNV]);
  const handleDoiTacChange = async (value: any) => {
    try {
      const result = await getListSanPhamTheoDoiTacNV({ma_doi_tac_ql: value, nv: chiTietChuongTrinhBaoHiem?.nv});
      // The function now properly returns the expected object
      return result;
    } catch (error) {
      console.error("Error fetching san pham:", error);
      return {data: [], tong_so_dong: 0};
    }
  };
  const setErrorFormFields = useCallback(
    (name: string, errors: string[]) => {
      formChiTietCTBH.setFields([
        {
          name,
          errors,
        },
      ]);
    },
    [formChiTietCTBH],
  );
  //XỬ LÝ ĐIỀU KIỆN ĐỂ MỞ SELECT
  const checkDieuKienMoSelect = (inputName: string, isOpen: boolean) => {
    //NẾU CHƯA CÓ DATA ĐỐI TÁC MÀ CHỌN CÁC INPUT TƯƠNG ỨNG -> HIỂN THỊ LỖI
    const arrInputNameTheoDoiTacNV: Array<string> = ["ma_sp"];

    if (arrInputNameTheoDoiTacNV.includes(inputName) && isOpen)
      if (!watchDoiTac) {
        setErrorFormFields("ma_doi_tac_ql", ["Vui lòng chọn đối tác "]);
      }
    if (!watchNV) {
      setErrorFormFields("nv", ["Vui lòng chọn nghiệp vụ "]);
    }
  };
  const closeModal = () => {
    setIsOpen(false);
    setChiTietChuongTrinhBaoHiem(null);
    formChiTietCTBH.resetFields();
    setFilterParams(filterParams);
  };

  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatChuongTrinhBaoHiemParams = formChiTietCTBH.getFieldsValue();
      const params: ReactQuery.ICapNhatChuongTrinhBaoHiemParams = {
        ...values,
        ngay_ad: dayjs(values.ngay_ad).format("YYYYMMDD"),
      };
      await onUpdateChuongTrinhBaoHiem(params);
      setIsOpen(false);
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const handleTabChange = async (activeKey: string) => {
    setActiveTab(activeKey);
    if (activeKey === "2") {
      console.log("Entering tab 2 logic");
      try {
        if (chiTietChuongTrinhBaoHiem && chiTietChuongTrinhBaoHiem.ma && chiTietChuongTrinhBaoHiem.ma_doi_tac_ql) {
          const values = {
            ma_ctbh: chiTietChuongTrinhBaoHiem.ma,
            ma_doi_tac_ql: chiTietChuongTrinhBaoHiem.ma_doi_tac_ql,
            nv: chiTietChuongTrinhBaoHiem.nv,
          };
          setFilterValues({
            ma: values.ma_ctbh,
            ma_doi_tac_ql: values.ma_doi_tac_ql,
            nv: values.nv,
          });
          await getListGoiBaoHiemTheoCTBH(values);
        }
      } catch (error) {
        console.log("Lỗi khi lấy giá trị hoặc gọi API:", error);
      }
    }
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderFooter = () => {
    return (
      <Form.Item className="float-right mb-0">
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle="Lưu"
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  const renderTabs = () => {
    const isEdit = !!chiTietChuongTrinhBaoHiem;

    return (
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <Tabs.TabPane key="1" tab={isEdit ? "Thông tin chương trình bảo hiểm" : "Tạo mới "}>
          {renderForm()}
          {renderFooter()}
        </Tabs.TabPane>
        <Tabs.TabPane key="2" tab="Thông tin gói áp dụng">
          <TabThongTinGoiBaoHiem filterValues={filterValues} chiTietChuongTrinhBaoHiem={chiTietChuongTrinhBaoHiem ?? undefined} />
        </Tabs.TabPane>
      </Tabs>
    );
  };
  const renderForm = () => (
    <Form form={formChiTietCTBH} layout="vertical" initialValues={{trang_thai: TRANG_THAI_CHI_TIET_CTBH[0].ma}}>
      <Row gutter={16}>
        {renderFormInputColum({...ma, disabled: chiTietChuongTrinhBaoHiem ? true : false}, 6)}
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, onChange: handleDoiTacChange, disabled: chiTietChuongTrinhBaoHiem ? true : false}, 6)}
        {renderFormInputColum({...nv, options: NGHIEP_VU_CTBH, disabled: chiTietChuongTrinhBaoHiem ? true : false}, 6)}
        {renderFormInputColum(
          {...ma_sp, options: listSanPhamSelect, onDropdownVisibleChange: (open: boolean) => checkDieuKienMoSelect(ma_sp.name, open), disabled: chiTietChuongTrinhBaoHiem ? true : false},
          6,
        )}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...ten}, 6)}
        {renderFormInputColum({...ngay_ad}, 6)}
        {renderFormInputColum({...stt}, 6)}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_CHI_TIET_CTBH}, 6)}
      </Row>
      <Row gutter={16}>{renderFormInputColum({...mo_ta}, 24)}</Row>
    </Form>
  );

  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        style={{
          top: 50,
        }}
        title={
          <HeaderModal
            title={chiTietChuongTrinhBaoHiem ? ` ${chiTietChuongTrinhBaoHiem.ten}` : "Tạo mới"}
            trang_thai_ten={chiTietChuongTrinhBaoHiem?.trang_thai_ten}
            trang_thai={chiTietChuongTrinhBaoHiem?.trang_thai}
          />
        }
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "65%",
          sm: "65%",
          md: "65%",
          lg: "65%",
          xl: "65%",
          xxl: "65%",
        }}
        footer={null}
        className="[&_.ant-space]:w-full">
        {renderTabs()}
      </Modal>
    </Flex>
  );
});

ModalChiTietChuongTrinhBaoHiemComponent.displayName = "ModalChiTietChuongTrinhBaoHiemComponent";
export const ModalChiTietChuongTrinhBaoHiem = memo(ModalChiTietChuongTrinhBaoHiemComponent, isEqual);
