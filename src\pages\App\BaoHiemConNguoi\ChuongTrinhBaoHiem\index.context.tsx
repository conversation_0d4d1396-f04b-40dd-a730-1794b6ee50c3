import {createContext, useContext} from "react";
import {ChuongTrinhBaoHiemContextProps} from "./index.model";
export const ChuongTrinhBaoHiemContext = createContext<ChuongTrinhBaoHiemContextProps>({
  loading: false,
  tongSoDong: 0,
  danhSachGoiBaoHiemPhanTrang: [],
  danhSachChuongTrinhBaoHiemPhanTrang: [],
  listGoiBaoHiem: [],
  listDoiTac: [],
  listSanPham: [],
  filterParams: {},
  setFilterParams: () => {},
  getListSanPhamTheoDoiTacNV: () => Promise.resolve(null),
  getListGoiBaoHiemTheoCTBH: () => Promise.resolve(null),
  layDanhSachChuongTrinhBaoHiemPhanTrang: () => {},
  onUpdateChuongTrinhBaoHiem: () => Promise.resolve(null),
  layChiTietChuongTrinhBaoHiem: () => Promise.resolve(null),
  getListDoiTac: () => Promise.resolve(),
  onXoaGoiBaoHiemKhoiCTBH: () => Promise.resolve(null),
  layDanhSachGoiBaoHiemPhanTrang: () => {},
  onUpdateGoiTrongCTBH: () => Promise.resolve(null),
});
export const useChuongTrinhBaoHiemContext = () => useContext(ChuongTrinhBaoHiemContext);
