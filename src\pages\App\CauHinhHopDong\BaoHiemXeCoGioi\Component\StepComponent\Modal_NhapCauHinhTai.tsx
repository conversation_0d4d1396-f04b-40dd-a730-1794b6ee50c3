import {CheckOutlined, CloseOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle, useState, useMemo} from "react";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {formCauHinhTaiBHInputConfigs, listKieuTai} from "./Constant";

export interface IModalCauHinhTaiBHRef {
  open: (data?: CommonExecute.Execute.ITaiBaoHiem) => void;
  close: () => void;
}

const {ma_dvi_tai, kieu_tai, tl_tai, so_tham_chieu} = formCauHinhTaiBHInputConfigs;

const ModalNhapCauHinhTaiComponent = forwardRef<IModalCauHinhTaiBHRef, {disabled?: boolean}>(({disabled = false}, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataChiTietTaiBH?: CommonExecute.Execute.ITaiBaoHiem) => {
      setIsOpen(true);
      if (dataChiTietTaiBH) {
        initFormInputData(dataChiTietTaiBH ?? ({} as CommonExecute.Execute.ITaiBaoHiem));
      }
    },
    close: () => setIsOpen(false),
  }));

  const {loading, chiTietHopDongBaoHiemXe, updateCauHinhTaiBaoHiem, listDonViDongTai} = useBaoHiemXeCoGioiContext();

  const [formNhapCauHinhTaiBH] = Form.useForm();
  const formValues = Form.useWatch([], formNhapCauHinhTaiBH);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Hàm filter listDonViDongTai khi tai_bh = C
  const filteredListDonViTaiBH = useMemo(() => {
    return listDonViDongTai.filter((item: any) => item.tai_bh === "C");
  }, [listDonViDongTai]);

  // init modal data
  const initFormInputData = (chiTiet: CommonExecute.Execute.ITaiBaoHiem) => {
    const arrFormData = [];
    if (chiTiet) {
      for (const key in chiTiet) {
        let value: any = chiTiet[key as keyof CommonExecute.Execute.ITaiBaoHiem];
        arrFormData.push({
          name: key,
          value: value,
        });
      }
    }
    formNhapCauHinhTaiBH.setFields(arrFormData);
  };

  //xử lý validate form
  useEffect(() => {
    formNhapCauHinhTaiBH
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formNhapCauHinhTaiBH, formValues]);

  //Bấm Update
  const onPressUpdateCauHinhTaiBH = async () => {
    try {
      const values: ReactQuery.IUpdateCauHinhTaiBHParams = formNhapCauHinhTaiBH.getFieldsValue(); //lấy ra values của form
      const cleanedValues = {
        ...values,
        so_id: chiTietHopDongBaoHiemXe.so_id ?? 0,
      };
      const response = await updateCauHinhTaiBaoHiem(cleanedValues);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onPressUpdateCauHinhTaiBH", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    formNhapCauHinhTaiBH.resetFields();
  };

  const renderFormInputColum = (props?: any, span = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formNhapCauHinhTaiBH" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressUpdateCauHinhTaiBH}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin cấu hình tái BH không?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit || disabled}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="custom-full-modal"
        title={<HeaderModal title="Thông tin tái bảo hiểm" />}
        // centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"40vw"}
        maskClosable={false}
        footer={renderFooter}>
        <Form id="formNhapCauHinhTaiBH" onFinish={onPressUpdateCauHinhTaiBH} form={formNhapCauHinhTaiBH} layout="vertical">
          <Row gutter={16} className="mt-4">
            {renderFormInputColum({...ma_dvi_tai, options: filteredListDonViTaiBH})}
            {renderFormInputColum({...kieu_tai, options: listKieuTai})}
            {renderFormInputColum({...tl_tai})}
            {renderFormInputColum({...so_tham_chieu})}
          </Row>
        </Form>
      </Modal>
    </Flex>
  );
});

export const ModalNhapCauHinhTai = memo(ModalNhapCauHinhTaiComponent, isEqual);
