import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {
  FormTaoMoiNhomHangMucXe, 
  TRANG_THAI_TAO_MOI_NHOM_HANG_MUC_XE, 
  DANH_SACH_NGHIEP_VU_XE
} from "../index.configs";
import {useNhomHangMucXeContext} from "../index.context";

export interface IModalChiTietNhomHangMucXeRef {
  open: (dataNhomHangMucXe?: CommonExecute.Execute.INhomHangMucXe) => void; // Mở modal với data (edit) hoặc không có data (create)
  close: () => void; 
}

export interface ChiTietNhomHangMucXeProps {
  onAfterSave?: () => void; // Callback sau khi save thành công
}

// ===== DESTRUCTURING FORM CONFIG =====
const {ma, ten, nv, stt, trang_thai} = FormTaoMoiNhomHangMucXe;

/**
 * Modal để tạo mới hoặc chỉnh sửa thông tin nhóm hạng mục xe
*/
const ModalChiTietNhomHangMucXeComponent = forwardRef<IModalChiTietNhomHangMucXeRef, ChiTietNhomHangMucXeProps>(({onAfterSave}, ref) => {
  const {listNhomHangMucXe, capNhatChiTietNhomHangMucXe, loading} = useNhomHangMucXeContext();
  
  /**
   * Cung cấp methods open/close cho component cha thông qua ref
   */
  useImperativeHandle(ref, () => ({
    open: (dataNhomHangMucXe?: CommonExecute.Execute.INhomHangMucXe) => {
      setIsOpen(true);
      if (dataNhomHangMucXe) {
        setChiTietNhomHangMucXe(dataNhomHangMucXe); // Chế độ sửa: load dữ liệu vào form
      } else {
        setChiTietNhomHangMucXe(null); // Chế độ tạo mới: reset form
        form.resetFields(); 
      }
    },
    close: () => setIsOpen(false),
  }));

  // ===== STATE MANAGEMENT =====
  const [chiTietNhomHangMucXe, setChiTietNhomHangMucXe] = useState<CommonExecute.Execute.INhomHangMucXe | null>(null); // Dữ liệu chi tiết (null = tạo mới)
  const [isOpen, setIsOpen] = useState(false); // Trạng thái mở/đóng modal

  const [form] = Form.useForm(); 
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false); // Trạng thái disable nút submit
  const formValues = Form.useWatch([], form); // Watch tất cả form values để validate
  
  /**
   * Effect để load dữ liệu vào form khi modal mở
   * - Nếu có dữ liệu: load vào form (chế độ sửa)
   * - Nếu không có dữ liệu: reset form với giá trị mặc định (chế độ tạo mới)
   */
  useEffect(() => {
    if (chiTietNhomHangMucXe && isOpen) {
      // ===== CHẾ ĐỘ SỬA: LOAD DỮ LIỆU VÀO FORM =====
      const arrFormData = [];
      for (const key in chiTietNhomHangMucXe) {
        arrFormData.push({
          name: key,
          value: chiTietNhomHangMucXe[key as keyof CommonExecute.Execute.INhomHangMucXe],
        });
      }
      form.setFields(arrFormData);
    } else if (!chiTietNhomHangMucXe && isOpen) {
      // ===== CHẾ ĐỘ TẠO MỚI: RESET FORM VỚI GIÁ TRỊ MẶC ĐỊNH =====
      form.setFields([
        { name: 'ma', value: '' },
        { name: 'ten', value: '' },
        { name: 'nv', value: undefined },
        { name: 'stt', value: '' },
        { name: 'trang_thai', value: undefined }
      ]);
    }
  }, [chiTietNhomHangMucXe, isOpen, form]);

  /**
   * Effect để validate form mỗi khi form values thay đổi
   * Disable nút submit nếu form có lỗi
   */
  useEffect(() => {
    if (!isOpen) return; // Chỉ validate khi modal đang mở
    
    form
      .validateFields({validateOnly: true}) // Validate không hiển thị lỗi
      .then(() => {
        setDisableSubmit(false); // Không có lỗi -> enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // Có lỗi -> disable nút Lưu
      });
  }, [form, formValues, isOpen]);

  /**
   * ===== MODAL CLOSE HANDLER - XỬ LÝ ĐÓNG MODAL =====
   */
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietNhomHangMucXe(null);
    form.resetFields();
  }, []);

  /**
   * ===== FORM SUBMIT HANDLER - XỬ LÝ SUBMIT FORM =====
   */
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatNhomHangMucXeParams = form.getFieldsValue(); // Lấy giá trị form
      
      // ===== KIỂM TRA TRÙNG MÃ KHI TẠO MỚI =====
      if (!chiTietNhomHangMucXe) {
        for (let i = 0; i < listNhomHangMucXe.length; i++) {
          if (listNhomHangMucXe[i].ma === values.ma) {
            form.setFields([
              {
                name: "ma",
                errors: ["Mã nhóm hạng mục xe đã tồn tại!"],
              },
            ]);
            return;
          }
        }
      }

      // ===== GỌI API CẬP NHẬT =====
      await capNhatChiTietNhomHangMucXe(values);
      closeModal();
      onAfterSave?.(); // Callback để refresh data ở component cha
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  /**
   * Render footer với button Quay lại và Lưu
   * Sử dụng Popconfirm để xác nhận trước khi lưu
   */
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  /**
   * ===== RENDER HELPER - RENDER FORM INPUT COLUMN =====
   */
  const renderFormColum = (props: IFormInput, span: number = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  /**
   * ===== RENDER FORM - RENDER FORM VỚI CÁC FIELD CẦN THIẾT =====
   */
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietNhomHangMucXe ? true : false}, 8)}
        {renderFormColum({...ten}, 16)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...nv, options: DANH_SACH_NGHIEP_VU_XE}, 8)}
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_NHOM_HANG_MUC_XE}, 10)}
      </Row>
    </Form>
  );

  /**
   * Render modal với:
   * - Header động (hiển thị tên khi sửa, "Tạo mới" khi tạo mới)
   * - Form với các field cần thiết
   * - Footer với buttons
   */
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal 
            title={chiTietNhomHangMucXe ? `${chiTietNhomHangMucXe.ten}` : "Tạo mới nhóm hạng mục xe"} 
            trang_thai_ten={chiTietNhomHangMucXe?.trang_thai_ten} 
            trang_thai={chiTietNhomHangMucXe?.trang_thai} 
          />
        }
        maskClosable={false} // Không cho phép đóng modal bằng cách click outside
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter()}> {/* Custom footer */}
        {renderForm()} {/* Form content */}
      </Modal>
    </Flex>
  );
});

// ===== COMPONENT SETUP =====
ModalChiTietNhomHangMucXeComponent.displayName = "ModalChiTietNhomHangMucXeComponent";
export const ModalChiTietNhomHangMucXe = memo(ModalChiTietNhomHangMucXeComponent, isEqual); // Memo với deep comparison
