import {CloseOutlined, DownloadOutlined, ExclamationCircleOutlined, InfoCircleOutlined, PlusCircleOutlined, PlusOutlined, UploadOutlined, WarningOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, ModalImportExcel} from "@src/components";
import type {IModalImportExcelRef, IExcelRowData} from "@src/components/ModalImportExcel";
import {defaultPaginationTableProps, fillRowTableEmpty, useAsyncAction} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {Col, Form, message, Row, Table, Tabs} from "antd";
import {Dayjs} from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {defaultFormValueTimKiemDoiTuongBaoHiemXe} from "../../index.configs";
import {useBaoHiemXeCoGioiContext} from "../../index.context";
import {listHDVipSelect, ThongTinDoiTuongBaoHiemXeStepProps} from "../Constant";
import {
  FormTaoMoiDoiTuongBaoHiemXe,
  FormTimKiemDoiTuongBaoHiemXeCoGioi,
  initFormFieldsDoiTuong,
  listMucDichSuDungXe,
  listNamSanXuat,
  parseNumber,
  ruleRequired,
  tableDoiTuongColumn,
  TableDoiTuongColumnDataType,
} from "./Constant";
import {RenderDieuKhoanBoSungTable, RenderLoaiHinhNghiepVuTable} from "./index";

const {nd_tim} = FormTimKiemDoiTuongBaoHiemXeCoGioi;
const {
  ten,
  dchi,
  gcn,
  bien_xe,
  so_khung,
  so_may,
  loai_xe,
  hang_xe,
  hieu_xe,
  nam_sx,
  md_sd,
  so_cho,
  // so_nguoi_bh,
  trong_tai,
  so_lphu_xe,
  gia_tri,
  gio_hl,
  ngay_hl,
  gio_kt,
  ngay_kt,
  ngay_cap,
  vip,
  so_id_dt,
  so_id,
  dk,
  dkbs,
} = FormTaoMoiDoiTuongBaoHiemXe;

const PAGE_SIZE = 10; // khai báo khác default cho vừa màn hình

const ThongTinDoiTuongBaoHiemXeStepComponent = forwardRef<any, ThongTinDoiTuongBaoHiemXeStepProps>(({formNhapDoiTuongBaoHiemXe}: ThongTinDoiTuongBaoHiemXeStepProps, ref) => {
  useImperativeHandle(ref, () => ({
    resetForm: () => {},
  }));

  const {
    loading,
    tongSoDongDoiTuongBaoHiemXe,
    danhSachDoiTuongBaoHiemXe,
    timKiemPhanTrangDoiTuongBaoHiemXe,
    listLoaiXe,
    listHangXe,
    listHieuXe,
    layChiTietDoiTuongBaoHiemXe,
    chiTietHopDongBaoHiemXe,
    exportExcel,
  } = useBaoHiemXeCoGioiContext();
  const [formTimKiemPhanTrangDoiTuongBaoHiemXe] = Form.useForm();

  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams>(
    defaultFormValueTimKiemDoiTuongBaoHiemXe as ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams,
  );
  const [page, setPage] = useState(1);
  const [doiTuongXeSelected, setDoiTuongXeSelected] = useState<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi | null>(null);

  // Modal Import Excel ref
  const modalImportExcelRef = useRef<IModalImportExcelRef>(null);

  //watch
  const watchHangXe = Form.useWatch("hang_xe", formNhapDoiTuongBaoHiemXe);
  const watchBienXe = Form.useWatch("bien_xe", formNhapDoiTuongBaoHiemXe);

  // Hàm dùng chung để chọn đối tượng bảo hiểm xe
  const handleSelectDoiTuongBaoHiemXe = useCallback(
    async (record: any) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      const response = await layChiTietDoiTuongBaoHiemXe(record as ReactQuery.IChiTietDoiTuongBaoHiemXeParams);
      if (response) initFormFieldsDoiTuong(formNhapDoiTuongBaoHiemXe, response);
      setDoiTuongXeSelected(response || null);
    },
    [layChiTietDoiTuongBaoHiemXe, formNhapDoiTuongBaoHiemXe],
  );

  //DATA TABLE ĐỐI TƯỢNG BB
  const dataTableListDoiTuongBaoHiemXe = useMemo<Array<TableDoiTuongColumnDataType>>(() => {
    try {
      const tableData = danhSachDoiTuongBaoHiemXe.map((item: any, index: number) => ({
        ...item,
        bien_xe: `${item.bien_xe || "-"} / ${item.so_khung || "-"} / ${item.so_may || "-"}`,
        key: index.toString(),
      }));

      //nếu có dữ liệu thì chọn đối tượng đầu tiên trong mảng
      if (tableData.length > 0) handleSelectDoiTuongBaoHiemXe(tableData[0]);
      // Add empty rows if data is less than 10 rows
      const arrEmptyRow: Array<TableDoiTuongColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListPhongBan error", error);
      return [];
    }
  }, [danhSachDoiTuongBaoHiemXe]);

  //Dữ liệu hiệu xe theo hãng xe
  const listHieuXeFilter = useMemo(() => {
    return listHieuXe.filter(item => item.hang_xe === watchHangXe && item.nv === chiTietHopDongBaoHiemXe.ma_sp);
  }, [listHieuXe, watchHangXe]);

  const listHangXeFilter = useMemo(() => {
    return listHangXe.filter(item => item.nv === chiTietHopDongBaoHiemXe.ma_sp);
  }, [listHangXe, chiTietHopDongBaoHiemXe]);

  //hàm tìm kiếm phân trang
  const handleSearchAndPagination = useCallback(
    (values?: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams & Partial<ReactQuery.IPhanTrang>, pageArg?: number) => {
      const so_id = chiTietHopDongBaoHiemXe?.so_id;
      // if là bấm tìm kiếm else là onchange page
      if (values) {
        const cleanedValues = {
          ...values,
          nd_tim: values.nd_tim ?? "",
          dong_tai: values.dong_tai ?? "",
          so_id,
        };
        setSearchParams(cleanedValues);
        setPage(1);
        timKiemPhanTrangDoiTuongBaoHiemXe({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
      } else {
        const page = pageArg || 1;
        setPage(page);
        timKiemPhanTrangDoiTuongBaoHiemXe({
          ...searchParams,
          so_id,
          trang: page,
          so_dong: PAGE_SIZE,
        });
      }
    },
    [chiTietHopDongBaoHiemXe, searchParams],
  );

  // Handle export Excel using useAsyncAction //Tạo 1 biến isSubmiting riêng cho từng action
  const [handleExportExcel, isExportingExcel] = useAsyncAction(async () => {
    const so_id = chiTietHopDongBaoHiemXe?.so_id;
    if (!so_id) {
      message.error("Không có thông tin hợp đồng để xuất Excel!");
      return;
    }

    try {
      await exportExcel({so_id});
    } catch (error) {
      console.error("Export Excel error:", error);
      message.error("Có lỗi xảy ra khi xuất Excel!");
      throw error; // Re-throw để useAsyncAction có thể handle
    }
  });

  // Handle import Excel
  const handleImportExcel = useCallback(() => {
    modalImportExcelRef.current?.openFilePicker();
  }, []);

  // Handle confirmed data from Excel import
  const handleExcelDataConfirm = useCallback((data: IExcelRowData[]) => {
    console.log("Excel data confirmed:", data);
    message.success(`Đã import thành công ${data.length} dòng dữ liệu từ Excel!`);

    // TODO: Process the imported data
    // You can:
    // 1. Add the data to the current table
    // 2. Send to server for batch processing
    // 3. Show confirmation dialog
    // 4. Update the form with imported data

    // Example: Log sample data for debugging
    if (data.length > 0) {
      console.log("Sample imported row:", data[0]);
    }

    // Refresh the table data after import
    handleSearchAndPagination();
  }, []);

  // Handle cancel import
  const handleExcelImportCancel = useCallback(() => {
    console.log("Excel import cancelled");
  }, []);

  // RENDER
  const renderFormInputColum = (props?: any, span = 4) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };

  //render header table đối tượng
  const renderHeaderTableDoiTuongBaoHiemXe = () => {
    return (
      <div>
        <Form form={formTimKiemPhanTrangDoiTuongBaoHiemXe} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={handleSearchAndPagination}>
          <Row gutter={8} className="items-end">
            {renderFormInputColum({...nd_tim}, 24)}
            {/* <Col>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full"></Button>
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <Button
                  className="w-full"
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  onClick={() => {
                    formNhapDoiTuongBaoHiemXe.resetFields();
                    setDoiTuongXeSelected(null);
                  }}
                  loading={loading}></Button>
              </Form.Item>
            </Col> */}
          </Row>
        </Form>
      </div>
    );
  };

  // Helper function để render icon theo giá trị sdbs
  const iConSstyle = {marginRight: "8px", fontSize: "15px", fontWeight: "bold"};
  const renderSdbsIcon = (sdbs: string) => {
    switch (sdbs) {
      case "N": // Đối tượng mới
        return <PlusOutlined style={{...iConSstyle, color: "#52c41a"}} title="Đối tượng mới" />;
      case "S": // Đối tượng sửa đổi
        return <WarningOutlined style={{...iConSstyle, color: "orange"}} title="Đối tượng sửa đổi" />;
      case "K": // Đối tượng không sửa đổi
        return <InfoCircleOutlined style={{...iConSstyle, color: "#8c8c8c"}} title="Đối tượng không sửa đổi" />;
      case "H": // Đối tượng kết thúc hiệu lực
        return <CloseOutlined style={{...iConSstyle, color: "#ff4d4f"}} title="Đối tượng kết thúc hiệu lực" />;
      default:
        return null;
    }
  };
  //render bảng danh sách đối tượng XE
  const renderTableDanhSachDoiTuongXe = () => {
    // Tính tổng phí bảo hiểm
    const tongPhiBaoHiem = dataTableListDoiTuongBaoHiemXe.reduce((acc, curr) => {
      // Loại bỏ các dòng empty
      if (curr && curr.tong_phi && curr.key && !curr.key.toString().startsWith("empty")) {
        acc += parseNumber(curr.tong_phi);
      }
      return acc;
    }, 0);

    return (
      <div>
        <Table<TableDoiTuongColumnDataType>
          className="custom-table-title-padding mt-5"
          dataSource={dataTableListDoiTuongBaoHiemXe} //mảng dữ liệu record được hiển thị
          columns={
            (tableDoiTuongColumn || []).map(item => {
              // Override render function cho column bien_xe để thêm icon
              if ("dataIndex" in item && item.dataIndex === "bien_xe") {
                return {
                  ...item,
                  render: (text: any, record: any) => {
                    if (record.key && record.key.toString().includes("empty")) return "\u00A0";

                    const icon = renderSdbsIcon(record.sdbs);
                    const bienXeText = text !== undefined ? text : "";

                    return (
                      <span className="flex items-center">
                        {icon}
                        {bienXeText}
                      </span>
                    );
                  },
                };
              }
              return {...item};
            }) || []
          } //định nghĩa cột của table
          loading={loading} //hiển thị loading khi đang gọi API để loading data
          sticky
          rowClassName={record => (record.so_id_dt && record.so_id_dt === doiTuongXeSelected?.gcn?.so_id_dt ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
          pagination={{
            ...defaultPaginationTableProps,
            total: tongSoDongDoiTuongBaoHiemXe,
            pageSize: PAGE_SIZE,
            current: page, //set current page
            onChange: (page, pageSize) => {
              handleSearchAndPagination(undefined, page);
            },
            locale: {
              jump_to: "Tới trang",
              page: "",
            },
          }}
          showHeader={true}
          title={renderHeaderTableDoiTuongBaoHiemXe}
          bordered //set border cả table
          onRow={record => {
            return {
              style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
              onClick: async () => {
                await handleSelectDoiTuongBaoHiemXe(record);
              }, // click row
            };
          }}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                {/* Cột đầu tiên: merge các cột trước cột phí bảo hiểm */}
                <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                  <div className="text-center font-medium">Tổng phí bảo hiểm</div>
                </Table.Summary.Cell>
                {/* Cột phí bảo hiểm: hiển thị tổng */}
                <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                  <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiem)}</div>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </div>
    );
  };

  //render form nhập thông tin đối tượng
  const renderForm = () => (
    <Form form={formNhapDoiTuongBaoHiemXe} layout="vertical" className="ml-2 mt-4">
      <Row gutter={16}>
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...dchi}, 8)}
        {renderFormInputColum({...bien_xe})}
        {renderFormInputColum({
          ...so_khung,
          rules: !watchBienXe ? [ruleRequired] : [],
        })}
        {renderFormInputColum({
          ...so_may,
          rules: !watchBienXe ? [ruleRequired] : [],
        })}
      </Row>

      <Row gutter={16}>
        {renderFormInputColum({...gcn}, 8)}
        {renderFormInputColum({...nam_sx, options: listNamSanXuat})}
        {renderFormInputColum({...so_lphu_xe})}
        {renderFormInputColum({...md_sd, options: listMucDichSuDungXe})}
        {renderFormInputColum({...gia_tri})}
      </Row>

      <Row gutter={16}>
        {renderFormInputColum({...loai_xe, options: listLoaiXe}, 8)}
        {renderFormInputColum({
          ...hang_xe,
          options: listHangXeFilter,
          onChange: (value: string) => {
            formNhapDoiTuongBaoHiemXe.setFieldValue("hieu_xe", undefined);
          },
        })}
        {renderFormInputColum({...hieu_xe, options: listHieuXeFilter})}
        {renderFormInputColum({...so_cho})}
        {renderFormInputColum({...trong_tai})}

        {/* {renderFormInputColum({...so_nguoi_bh})} */}
      </Row>
      <Row gutter={16}>
        {renderFormInputColum({...vip, options: listHDVipSelect})}
        {renderFormInputColum({
          ...ngay_cap,
          onChange: (date: Dayjs) => {
            formNhapDoiTuongBaoHiemXe.setFields([
              {name: "ngay_hl", value: date},
              {name: "ngay_kt", value: date.add(1, "y")},
            ]);
          },
        })}
        {renderFormInputColum({
          ...gio_hl,
          onChange: (date: Dayjs) => {
            formNhapDoiTuongBaoHiemXe.setFields([{name: "gio_kt", value: date}]);
          },
        })}
        {renderFormInputColum({
          ...ngay_hl,
          onChange: (date: Dayjs) => {
            formNhapDoiTuongBaoHiemXe.setFields([{name: "ngay_kt", value: date.add(1, "y")}]);
          },
        })}
        {renderFormInputColum({...gio_kt})}
        {renderFormInputColum({...ngay_kt})}
        {/* các input ẩn */}
        {renderFormInputColum({...so_id_dt})}
        {renderFormInputColum({...so_id})}
        {renderFormInputColum({...dk})}
        {renderFormInputColum({...dkbs})}
      </Row>
    </Form>
  );

  //render tabs sản phẩm bảo hiểm
  const renderTabs = () => {
    return (
      <div className="ml-2">
        <Tabs
          animated={true}
          size="small"
          defaultActiveKey="1"
          tabBarStyle={{marginBottom: "8px"}}
          items={[
            {
              key: "1",
              label: "Loại hình nghiệp vụ",
              children: (
                <RenderLoaiHinhNghiepVuTable
                  chiTietHopDongBaoHiem={chiTietHopDongBaoHiemXe}
                  chiTietDoiTuongBaoHiemXe={doiTuongXeSelected || ({} as CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi)}
                  onDataChange={data => {
                    formNhapDoiTuongBaoHiemXe.setFieldValue("dk", data);
                  }}
                />
              ),
            },
            {
              key: "2",
              label: "Điều khoản bổ sung",
              children: (
                <RenderDieuKhoanBoSungTable
                  chiTietHopDongBaoHiem={chiTietHopDongBaoHiemXe}
                  chiTietDoiTuongBaoHiemXe={doiTuongXeSelected || ({} as CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi)}
                  onDataChange={data => {
                    formNhapDoiTuongBaoHiemXe.setFieldValue("dkbs", data);
                  }}
                />
              ),
            },
          ]}
          // onChange={onChange}
        />
      </div>
    );
  };

  return (
    <>
      <Row>
        <Col span={6}>
          {/* Table danh sách đối tượng */}
          {renderTableDanhSachDoiTuongXe()}
          <Row gutter={8} className="mt-5">
            {/* <Col>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full"></Button>
              </Form.Item>
            </Col> */}
            <Col>
              <Button type="dashed" icon={<UploadOutlined />} className="w-full" onClick={handleImportExcel}>
                Import
              </Button>
            </Col>
            <Col>
              <Button type="primary" loading={isExportingExcel} icon={<DownloadOutlined />} className="w-full" onClick={handleExportExcel}>
                Export
              </Button>
            </Col>
            <Col>
              <Button
                className="w-full"
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  formNhapDoiTuongBaoHiemXe.resetFields();
                  setDoiTuongXeSelected(null);
                }}
                loading={loading}>
                Thêm mới
              </Button>
            </Col>
          </Row>
        </Col>

        <Col span={18}>
          {/* Form nhập thông tin đối tượng */}
          {renderForm()}
          {/* Tabs loai hình nghiệp vụ, điều khoản bổ sung */}
          {renderTabs()}
        </Col>
      </Row>

      {/* Modal Import Excel */}
      <ModalImportExcel ref={modalImportExcelRef} onDataConfirm={handleExcelDataConfirm} onCancel={handleExcelImportCancel} />
    </>
  );
});

ThongTinDoiTuongBaoHiemXeStepComponent.displayName = "ThongTinDoiTuongBaoHiemXeStepComponent";
export const ThongTinDoiTuongBaoHiemXeStep = memo(ThongTinDoiTuongBaoHiemXeStepComponent, isEqual);
