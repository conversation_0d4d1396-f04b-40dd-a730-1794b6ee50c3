import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

//form tìm kiếm
export interface IFormTimKiemChuongTrinhBaoHiemFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemChuongTrinhBaoHiem: IFormTimKiemChuongTrinhBaoHiemFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
  },
  ma: {
    component: "input",
    name: "ma",
    label: "Mã chương trình ",
    placeholder: "<PERSON>ã chương trình ",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên chương trình ",
    placeholder: "Tên chương trình ",
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
};

export interface TableChuongTrinhBaoHiemDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_doi_tac_ql?: string;
  nv?: string;
  ten_nv?: string;
  ngay_ad?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
  trang_thai?: string;
  ma_sp?: string;
  ten_sp?: string;
}

export const chuongTrinhBaoHiemColumns: TableProps<TableChuongTrinhBaoHiemDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Nghiệp vụ", dataIndex: "nv", key: "nv", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Mã Sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày áp dụng", dataIndex: "ngay_ad", key: "ngay_ad", width: 130, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams = {
  ma_doi_tac_ql: "",
  ma: "",
  ten: "",
  nv: "",
  trang_thai: "",
};
export const radioItemTrangThaiChuongTrinhBaoHiemTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
export const TRANG_THAI_TK_CHUONG_TRINH_BAO_HIEM = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];
export const NGHIEP_VU_TK = [
  // {ten: "Bảo hiểm hỗn hợp", ma: "HH"},
  // {ten: "Bảo hiểm kỹ thuật", ma: "KT"},
  // {ten: "Bảo hiểm trách nhiệm", ma: "TN"},
  // {ten: "Bảo hiểm tài sản", ma: "TS"},
  {ten: "Bảo hiểm con người", ma: "NG"},
  {ten: "Bảo hiểm xe cơ giới", ma: "XCG"},
  {ten: "Bảo hiểm tài sản", ma: "TS"},
];
export type DataIndex = keyof TableChuongTrinhBaoHiemDataType;
