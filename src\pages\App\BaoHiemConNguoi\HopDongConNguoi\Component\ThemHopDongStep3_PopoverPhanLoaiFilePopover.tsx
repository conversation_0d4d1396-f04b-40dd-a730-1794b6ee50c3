import {Button, Input, List} from "antd";
import {useMemo, useState} from "react";
import {FormInput} from "@src/components";
import {useHopDongConNguoiContext} from "../index.context";

interface PhanLoaiPopoverContentProps {
  onSelect: (ma_hang_muc: string) => void;
  onClose: () => void;
}

const PhanLoaiPopoverContent = ({onSelect, onClose}: PhanLoaiPopoverContentProps) => {
  const {listHangMucXeTheoNhom} = useHopDongConNguoiContext();
  const [selectedHangMuc, setSelectedHangMuc] = useState("");
  const [searchText, setSearchText] = useState("");

  const handlePhanLoai = () => {
    if (selectedHangMuc) {
      onSelect(selectedHangMuc);
      onClose();
      setSelectedHangMuc("");
    }
  };

  // Lọ<PERSON> danh sách hạng mục theo từ khóa tìm kiếm
  const filteredHangMuc = useMemo(() => {
    if (!searchText.trim()) return listHangMucXeTheoNhom;
    return listHangMucXeTheoNhom.filter((item: any) => item.ten?.toLowerCase().includes(searchText.toLowerCase()) || item.ma?.toLowerCase().includes(searchText.toLowerCase()));
  }, [listHangMucXeTheoNhom, searchText]);

  return (
    <div className="flex h-[45vh] min-w-[300px] max-w-[350px] flex-col">
      {/* Input tìm kiếm */}
      <Input placeholder="Tìm kiếm hạng mục..." value={searchText} onChange={e => setSearchText(e.target.value)} style={{marginBottom: 8}} allowClear />

      {/* Danh sách hạng mục với List + FormInput checkbox, chỉ chọn được 1 */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {filteredHangMuc && filteredHangMuc.length > 0 ? (
          <List
            dataSource={filteredHangMuc}
            style={{
              flex: 1,
              overflowY: "auto",
              overflowX: "hidden",
              paddingRight: 4, // Thêm padding để tránh thanh cuộn che nội dung
            }}
            renderItem={(item: any) => (
              <div onClick={() => setSelectedHangMuc(selectedHangMuc === item.ma ? "" : item.ma)} key={item.value} className="mb-0 flex flex-row items-center gap-1">
                <FormInput
                  className="!mb-0 mr-1"
                  component="checkbox"
                  checked={selectedHangMuc === item.ma}
                  onChange={() => setSelectedHangMuc(selectedHangMuc === item.ma ? "" : item.ma)}
                  label={undefined}
                />
                <span>{item.ten}</span>
              </div>
            )}
          />
        ) : (
          <div className="flex flex-1 items-center justify-center">{searchText ? `Không tìm thấy hạng mục nào cho "${searchText}"` : "Không có hạng mục nào"}</div>
        )}
      </div>

      <div className="mt-auto flex justify-end gap-2 border-t border-[#f0f0f0] pt-3">
        <Button size="small" onClick={onClose}>
          Hủy
        </Button>
        <Button type="primary" size="small" disabled={!selectedHangMuc} onClick={handlePhanLoai}>
          Phân loại
        </Button>
      </div>
    </div>
  );
};

export default PhanLoaiPopoverContent;
