import {CheckOutlined, CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, Modal, Row, Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useEffect, useImperativeHandle, useMemo, useState} from "react";
import {useBaoHiemXeCoGioiContext} from "../index.context";
import {FormTimKiemNguoiPheDuyet, FormTrinhDuyet, tableNguoiPheDuyetColumn, TableNguoiPheDuyetColumnDataType} from "./Constant";

export interface IModalTrinhDuyetHopDongRef {
  open: () => void;
  close: () => void;
}

const PAGE_SIZE = 8; // khai b<PERSON>o khác default cho vừa màn hình
const {nd_tim} = FormTimKiemNguoiPheDuyet;
const {nd_trinh} = FormTrinhDuyet;

const ModalTrinhDuyetHopDongXeComponent = forwardRef<IModalTrinhDuyetHopDongRef>((props, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setIsOpen(true);
      getDanhSachNguoiDuyetHopDong({so_id: chiTietHopDongBaoHiemXe?.so_id});
    },
    close: () => setIsOpen(false),
  }));

  const {loading, danhSachNguoiDuyetHopDong, getDanhSachNguoiDuyetHopDong, chiTietHopDongBaoHiemXe, trinhPheDuyetHopDong} = useBaoHiemXeCoGioiContext();

  const [formTimKiemNguoiPheDuyet] = Form.useForm();
  const [formTrinhDuyetHopDong] = Form.useForm();
  const formValues = Form.useWatch([], formTrinhDuyetHopDong);
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<CommonExecute.Execute.INguoiPheDuyetHopDongXe | null>(null);

  // khởi tạo dữ liệu cho bảng
  const dataTableListNguoiDuyetHopDong = useMemo(() => {
    try {
      const tableData = danhSachNguoiDuyetHopDong.map((item: any, index: number) => ({
        ...item,
        sott: index + 1,
        key: index.toString(),
      }));
      const arrEmptyRow: Array<TableNguoiPheDuyetColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      return [];
    }
  }, [danhSachNguoiDuyetHopDong]);

  //bấm tìm kiếm người duyệt
  const handleSearchApi = (values: ReactQuery.ILietKeDanhSachNguoiDuyetParams) => {
    const cleanValues = {
      ...values,
      so_id: chiTietHopDongBaoHiemXe?.so_id,
    };
    getDanhSachNguoiDuyetHopDong(cleanValues);
  };

  //Validate form
  useEffect(() => {
    formTrinhDuyetHopDong
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(!selectedItem); // nếu form hợp lệ và có selectedItem -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu form có lỗi -> cho disable nút Lưu
      });
  }, [formTrinhDuyetHopDong, formValues, selectedItem]);

  //Bấm Update
  const onPressTrinhDuyetHopDong = async () => {
    try {
      const values: ReactQuery.ITrinhPheDuyetHopDongParams = formTrinhDuyetHopDong.getFieldsValue(); //lấy ra values của form
      const cleanedValues = {
        ...values,
        so_id: chiTietHopDongBaoHiemXe.so_id ?? 0,
        nv: chiTietHopDongBaoHiemXe.nv ?? "",
        nsd_duyet: selectedItem?.nsd_duyet,
      };
      const response = await trinhPheDuyetHopDong(cleanedValues);
      if (response) {
        onCloseModal();
        return;
      }
    } catch (error) {
      console.log("onPressTrinhDuyetHopDong", error);
    }
  };

  //bấm huỷ / để sau xoá dữ liệu của modal khi nào mở thì gọi lại
  const onCloseModal = () => {
    setIsOpen(false);
    formTimKiemNguoiPheDuyet.resetFields();
    formTrinhDuyetHopDong.resetFields();
    setSelectedItem(null);
  };

  //RENDER
  const renderFormInputColum = (props?: any, span = 10) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  //render cột
  const renderColumn = (column: TableColumnType<TableNguoiPheDuyetColumnDataType>) => {
    if (!("dataIndex" in column)) return column;
    return column;
  };

  //render header table đối tượng
  const renderHeaderTableDoiTuongBaoHiemXe = () => {
    return (
      <Form id="formTimKiemNguoiPheDuyet" form={formTimKiemNguoiPheDuyet} layout="vertical" className="mt-5 [&_.ant-form-item]:mb-2" onFinish={handleSearchApi}>
        <Row gutter={16} align={"bottom"}>
          {renderFormInputColum({...nd_tim})}
          <Col>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full">
                Tìm kiếm
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" htmlType="reset" form="formTimKiemPhanTrangNguoiPheDuyet" onClick={onCloseModal} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressTrinhDuyetHopDong}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn trình duyệt hợp đồng không?"
          buttonTitle={"Trình"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          loading={loading}
        />
      </Form.Item>
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="custom-full-modal"
        title={<HeaderModal title={"Trình phê duyệt"} />}
        centered
        open={isOpen}
        onOk={() => setIsOpen(false)}
        onCancel={onCloseModal}
        width={"70vw"}
        maskClosable={false}
        footer={renderFooter}>
        {renderHeaderTableDoiTuongBaoHiemXe()}
        <Table<TableNguoiPheDuyetColumnDataType>
          className={`${dataTableListNguoiDuyetHopDong.length <= 10 ? "hide-scrollbar" : ""} no-header-border-radius no-hover-table`}
          columns={tableNguoiPheDuyetColumn?.map(renderColumn)}
          dataSource={dataTableListNguoiDuyetHopDong}
          scroll={{y: 300}}
          bordered
          pagination={false}
          rowClassName={record => (record?.bt && record.bt === selectedItem?.bt ? "table-row-active" : "")} // xử lý việc 1 row được selected -> row đấy sẽ được highlight lên
          onRow={record => {
            return {
              style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
              onClick: () => {
                if (!record.nsd_duyet) return setSelectedItem(null);
                setSelectedItem(record as CommonExecute.Execute.INguoiPheDuyetHopDongXe);
              },
            };
          }}
        />

        <Form initialValues={{nd_trinh: "Kính trình"}} form={formTrinhDuyetHopDong} layout="vertical" className="mt-2">
          <FormInput {...nd_trinh} className="fix-textarea-height" />
        </Form>
      </Modal>
    </Flex>
  );
});
ModalTrinhDuyetHopDongXeComponent.displayName = "ModalTrinhDuyetHopDongXeComponent";
export const ModalTrinhDuyetHopDongXe = memo(ModalTrinhDuyetHopDongXeComponent, isEqual);
