import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDoiTac, TRANG_THAI_TAO_MOI_DOI_TAC} from "../index.configs";
import {useQuanLyDoiTacContext} from "../index.context";
import {ChiTietDoiTacProps, IModalChiTietDoiTacRef} from "./Constant";
const {ma, ten, ten_tat, ten_e, mst, dchi, dthoai, stt, trang_thai} = FormTaoMoiDoiTac;

const ModalChiTietDoiTacComponent = forwardRef<IModalChiTietDoiTacRef, ChiTietDoiTacProps>(({listDoiTac}: ChiTietDoiTacProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDoiTac?: CommonExecute.Execute.IDoiTac) => {
      setIsOpen(true);
      if (dataDoiTac) setChiTietDoiTac(dataDoiTac); // nếu có dữ liệu -> set chi tiết đối tác -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDoiTac, setChiTietDoiTac] = useState<CommonExecute.Execute.IDoiTac | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDoiTac, getListDoiTac, loading} = useQuanLyDoiTacContext();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDoiTac) {
      const arrFormData = [];
      for (const key in chiTietDoiTac) {
        arrFormData.push({
          name: key,
          value: chiTietDoiTac[key as keyof CommonExecute.Execute.IDoiTac],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDoiTac]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDoiTac(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateDoiTacParams = form.getFieldsValue(); //lấy ra values của form
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại
      if (!chiTietDoiTac) {
        for (let i = 0; i < listDoiTac.length; i++) {
          if (listDoiTac[i].ma === values.ma) {
            form.setFields([
              {
                name: "ma",
                errors: ["Mã đối tác đã tổn tại!"],
              },
            ]);
            return;
          }
        }
      }

      await capNhatChiTietDoiTac(values); //cập nhật lại đối tác
      await getListDoiTac(); //lấy lại danh sách đối tác
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput) => (
    <Col span={8}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDoiTac ? true : false})}
        {renderFormColum({...ten})}
        {renderFormColum({...ten_tat})}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormColum({...ten_e})}
        {renderFormColum({...mst})}
        {renderFormColum({...dthoai})}
      </Row>

      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormColum({...dchi})}
        {renderFormColum({...stt})}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DOI_TAC})}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietDoiTac ? `${chiTietDoiTac.ten}` : "Tạo mới đối tác"} trang_thai_ten={chiTietDoiTac?.trang_thai_ten} trang_thai={chiTietDoiTac?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"60%"}
        styles={{
          body: {
            height: "30vh",
            // overflowY: "auto",
            // overflowX: "hidden",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDoiTacComponent.displayName = "ModalChiTietDoiTacComponent";
export const ModalChiTietDoiTac = memo(ModalChiTietDoiTacComponent, isEqual);
