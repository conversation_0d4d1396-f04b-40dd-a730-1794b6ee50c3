import {IFormInput} from "@src/@types";
import {FormInput} from "@src/components";
import {Col, Divider, Form, Row} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {FormNguoiDuocBaoHiem, listGioiTinhSelect, listKhachHangVipSelect, ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemProps, ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemRef} from "./Constant";

const {
  ten,
  ngay_sinh,
  gioi_tinh,
  so_cmt,
  dthoai,
  email,
  dia_chi,
  ngay_cap,
  gcn,
  vip,
  gio_hl,
  ngay_hl,
  gio_kt,
  ngay_kt,
  ma_goi_bh, //nhóm gói bảo hiểm
  cnhanh_ctac,
  pban_ctac,
  cvu_ctac,
  cty_ctac,
  ma_nv_ctac,
  email_ctac,
} = FormNguoiDuocBaoHiem;

const TabThongTinNguoiDuocBaoHiemComponent = forwardRef<ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemRef, ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemProps>(
  ({formThongTinNguoiBaoHiem, setIsChangeData}: ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemProps, ref) => {
    useImperativeHandle(ref, () => ({}));

    const {listGoiBaoHiem, chiTietNguoiDuocBaoHiem} = useHopDongConNguoiContext();
    useEffect(() => {
      fillValueVaoForm();
    }, [chiTietNguoiDuocBaoHiem]);

    const fillValueVaoForm = useCallback(async () => {
      const arrFormData = [];
      const arrInputFormThongTinHopDong = Object.keys(FormNguoiDuocBaoHiem); //lấy ra key của form
      //chỉ điền những thuộc tính cần hiển thị lên input
      for (const key in chiTietNguoiDuocBaoHiem) {
        if (arrInputFormThongTinHopDong.includes(key)) {
          let value: any = chiTietNguoiDuocBaoHiem[key as keyof CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi];
          //xử lý các key đặc biệt
          if (key === "ngay_sinh") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          else if (key === "ngay_cap") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          else if (key === "ngay_hl") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          else if (key === "gio_hl") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
          else if (key === "ngay_kt") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          else if (key === "gio_kt") value = dayjs(value + "", "HH:mm").isValid() ? dayjs(value + "", "HH:mm") : "";
          // else if (key === "vip") value = value == "VIP" ? value : "K";
          arrFormData.push({
            name: key as keyof CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi,
            value: value,
          });
        }
      }
      formThongTinNguoiBaoHiem.setFields(arrFormData);
    }, [formThongTinNguoiBaoHiem, chiTietNguoiDuocBaoHiem]);

    const handleValuesChange = useCallback(
      (changedValues: any, allValues: any) => {
        setIsChangeData(true);
      },
      [setIsChangeData],
    );

    // RENDER
    const renderFormInputColum = (props: IFormInput, span = 6) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };

    return (
      <Form form={formThongTinNguoiBaoHiem} layout="vertical" className="ml-2" initialValues={{}} onValuesChange={handleValuesChange}>
        {/* gutter : khoảng cách giữa các ô */}
        <Row gutter={16}>
          {renderFormInputColum(ten)}
          {renderFormInputColum(ngay_sinh)}
          {renderFormInputColum({...gioi_tinh, options: listGioiTinhSelect})}
          {renderFormInputColum(so_cmt)}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum(dthoai)}
          {renderFormInputColum(email)}
          {renderFormInputColum(dia_chi, 12)}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum({...ma_goi_bh, options: listGoiBaoHiem})}
          {renderFormInputColum(ngay_cap)}
          {renderFormInputColum(gcn)}
          {renderFormInputColum({...vip, options: listKhachHangVipSelect})}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum({
            ...gio_hl,
            onChange: time => {
              formThongTinNguoiBaoHiem.setFieldValue("gio_kt", time);
            },
          })}
          {renderFormInputColum({
            ...ngay_hl,
            // minDate: watchNgayCap,
            onChange: date => {
              // ngày kết thúc = ngày hiệu lực + 1
              formThongTinNguoiBaoHiem.setFields([
                {
                  name: "ngay_kt",
                  value: dayjs(date).add(1, "y"),
                },
              ]);
            },
          })}
          {renderFormInputColum(gio_kt)}
          {renderFormInputColum({
            ...ngay_kt,
          })}
        </Row>
        <Divider className="!my-2" />
        <Row gutter={16}>
          {renderFormInputColum(cty_ctac)}
          {renderFormInputColum(cnhanh_ctac)}
          {renderFormInputColum(pban_ctac)}
          {renderFormInputColum(cvu_ctac)}
        </Row>
        <Row gutter={16}>
          {renderFormInputColum(ma_nv_ctac)}
          {renderFormInputColum(email_ctac)}
        </Row>
      </Form>
    );
  },
);

TabThongTinNguoiDuocBaoHiemComponent.displayName = "TabThongTinNguoiDuocBaoHiemComponent";
export const ThemHopDongStep2_TabThongTinNguoiDuocBaoHiem = memo(TabThongTinNguoiDuocBaoHiemComponent, isEqual);
