import {TableProps} from "antd";

import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";

/**
 * Function này kiểm tra xem một giá trị có rỗng hay không
 */
const isEmptyValue = (value: any): boolean => {
  return value === null || value === undefined || value === "" || (typeof value === "string" && value.trim() === "");
};

//ĐỊNH NGHĨA CÁC FIELD TRONG FORM TÌM KIẾM
export interface IFormTimKiemHangMucXeFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  nv: IFormInput;
  nhom: IFormInput;
  trang_thai: IFormInput;
}

/**
 * Object này định nghĩa các field tìm kiếm hiển thị ở phần header của table
 */
export const FormTimKiemDanhMucHangMucXe: IFormTimKiemHangMucXeFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã hạng mục",
    placeholder: "Nhập mã hạng mục",
    className: "!mb-0",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên hạng mục",
    placeholder: "Nhập tên hạng mục",
    className: "!mb-0",
  },
  // loai: {
  //   component: "input",
  //   name: "loai",
  //   label: "Loại",
  //   placeholder: "Nhập loại",
  //   className: "!mb-0",
  // },
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    placeholder: "Chọn nghiệp vụ",
    className: "!mb-0",
  },
  nhom: {
    component: "select",
    name: "nhom",
    label: "Nhóm",
    placeholder: "Chọn nhóm",
    className: "!mb-0",
  },
  // vi_tri: {
  //   component: "input",
  //   name: "vi_tri",
  //   label: "Vị trí",
  //   placeholder: "Nhập vị trí",
  //   className: "!mb-0",
  // },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    className: "!mb-0",
  },
};

// ===== INTERFACE FORM MODAL CHI TIẾT - ĐỊNH NGHĨA CÁC FIELD TRONG MODAL TẠO MỚI/SỬA =====
export interface IFormTaoMoiHangMucXeFieldsConfig {
  nv: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  nhom: IFormInput;
  vi_tri: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
}

/**
 * Object này định nghĩa các field trong modal để tạo mới hoặc chỉnh sửa hạng mục xe
 */
export const FormTaoMoiHangMucXe: IFormTaoMoiHangMucXeFieldsConfig = {
  nv: {
    component: "select",
    label: "Nghiệp vụ",
    name: "nv",
    placeholder: "Chọn nghiệp vụ",
    rules: [ruleInputMessage.required],
  },
  ma: {
    component: "input",
    label: "Mã hạng mục",
    name: "ma",
    placeholder: "Nhập mã hạng mục",
    rules: [ruleInputMessage.required],
  },
  ten: {
    component: "input",
    label: "Tên hạng mục",
    name: "ten",
    placeholder: "Nhập tên hạng mục",
    rules: [ruleInputMessage.required],
  },
  loai: {
    component: "select",
    label: "Loại",
    name: "loai",
    placeholder: "Chọn loại",
    rules: [ruleInputMessage.required],
  },
  nhom: {
    component: "select",
    label: "Nhóm",
    name: "nhom",
    placeholder: "Chọn nhóm",
    rules: [ruleInputMessage.required],
  },
  vi_tri: {
    component: "select",
    label: "Vị trí",
    name: "vi_tri",
    placeholder: "Chọn vị trí",
    rules: [ruleInputMessage.required],
  },
  stt: {
    component: "input",
    label: "Số thứ tự",
    name: "stt",
    placeholder: "Nhập số thứ tự",
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleInputMessage.required],
  },
};

/**
 * Object này là cấu hình cũ, được giữ lại để đảm bảo tương thích với code cũ
 */
export const FormModalChiTietHangMucXe: Record<string, IFormInput> = {
  nv: {
    name: "nv",
    label: "Nghiệp vụ",
    required: true,
    placeholder: "Chọn nghiệp vụ",
    component: "select",
    options: [],
  },
  ma: {
    name: "ma",
    label: "Mã hạng mục",
    required: true,
    placeholder: "Nhập mã hạng mục",
    component: "input",
  },
  ten: {
    name: "ten",
    label: "Tên hạng mục",
    required: true,
    placeholder: "Nhập tên hạng mục",
    component: "input",
  },
  loai: {
    name: "loai",
    label: "Loại",
    required: false,
    placeholder: "Chọn loại",
    component: "select",
    options: [],
  },
  nhom: {
    name: "nhom",
    label: "Nhóm",
    required: true,
    placeholder: "Chọn nhóm",
    component: "select",
    options: [],
  },
  vi_tri: {
    name: "vi_tri",
    label: "Vị trí",
    required: false,
    placeholder: "Chọn vị trí",
    component: "select",
    options: [],
  },

  stt: {
    name: "stt",
    label: "Số thứ tự",
    required: false,
    placeholder: "Nhập số thứ tự",
    component: "input",
  },
  trang_thai: {
    name: "trang_thai",
    label: "Trạng thái",
    required: true,
    placeholder: "Chọn trạng thái",
    component: "select",
    options: [],
  },
};

// Options cho dropdown trạng thái trong form tìm kiếm
export const radioItemTrangThaiHangMucXeSelect: Array<{ma: string; ten: string}> = [
  {ma: "", ten: "Tất cả"},
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];

export const radioItemTrangThaiHangMucXeTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];

// ===== TRẠNG THÁI TẠO MỚI =====
export const TRANG_THAI_TAO_MOI_HANG_MUC_XE = [
  {value: "D", label: "Đang sử dụng"},
  {value: "K", label: "Ngừng sử dụng"},
];

/**
 * Danh sách các nghiệp vụ bảo hiểm có trong hệ thống
 */
export const DANH_SACH_NGHIEP_VU_HANG_MUC_XE = [
  // {ten: "Bảo hiểm con người", ma: "NG"},
  {ten: "Bảo hiểm ô tô", ma: "XE"},
  {ten: "Bảo hiểm xe máy", ma: "XE_MAY"},
];
export const DANH_SACH_VI_TRI = [
  {ten: "Bên trái", ma: "TRAI"},
  {ten: "Bên phải", ma: "PHAI"},
  {ten: "Đằng trước", ma: "TRUOC"},
  {ten: "Đằng sau", ma: "SAU"},
  {ten: "Phía trên", ma: "TREN"},
  {ten: "Phía dưới", ma: "DUOI"},
];
export const DANH_SACH_LOAI = [
  {ten: "Hạng mục chính", ma: "CHINH"},
  {ten: "Hạng mục phụ", ma: "PHU"},
];

/**
 * Danh sách nghiệp vụ có thêm option "Tất cả" để tìm kiếm tất cả nghiệp vụ
 */
export const DANH_SACH_NGHIEP_VU_TIM_KIEM_HANG_MUC_XE = [{ten: "Tất cả", ma: ""}, ...DANH_SACH_NGHIEP_VU_HANG_MUC_XE];
export const DANH_SACH_VI_TRI_TIM_KIEM_HANG_MUC_XE = [{ten: "Tất cả", ma: ""}, ...DANH_SACH_VI_TRI];
export const DANH_SACH_LOAI_TIM_KIEM_HANG_MUC_XE = [{ten: "Tất cả", ma: ""}, ...DANH_SACH_LOAI];
/**
 * Interface này định nghĩa structure của mỗi row trong table hạng mục xe
 */
export interface TableHangMucXeColumnDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  nv?: string;
  ten_nv?: string;
  loai?: string;
  nhom?: string;
  vi_tri?: string;
  stt?: number;
  trang_thai?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

/**
 * Array này định nghĩa các cột hiển thị trong bảng danh sách hạng mục xe
 */
export const tableHangMucXeColumn: TableProps<TableHangMucXeColumnDataType>["columns"] = [
  {
    title: "STT",
    dataIndex: "sott",
    key: "sott",
    align: "center",
    width: colWidthByKey.sott,
    ...defaultTableColumnsProps,
  },
  {
    title: "Mã hạng mục",
    dataIndex: "ma",
    key: "ma",
    align: "center",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Tên hạng mục",
    dataIndex: "ten",
    key: "ten",
    align: "left",
    width: 200,
    ...defaultTableColumnsProps,
  },
  {
    title: "Nghiệp vụ",
    dataIndex: "nv_ten",
    key: "nv_ten",
    align: "center",
    width: 150,
    ...defaultTableColumnsProps,
  },
  {
    title: "Loại",
    dataIndex: "loai_ten",
    key: "loai_ten",
    align: "center",
    width: 120,
    ...defaultTableColumnsProps,
  },
  {
    title: "Nhóm",
    dataIndex: "nhom",
    key: "nhom",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  {
    title: "Vị trí",
    dataIndex: "vi_tri_ten",
    key: "vi_tri_ten",
    align: "center",
    width: 100,
    ...defaultTableColumnsProps,
  },
  // {
  //   title: "STT",
  //   dataIndex: "stt",
  //   key: "stt",
  //   align: "center",
  //   width: 30,
  //   ...defaultTableColumnsProps,
  // },

  {
    title: "Ngày tạo",
    dataIndex: "ngay_tao",
    key: "ngay_tao",
    align: "center",
    width: colWidthByKey.ngay_tao,
    ...defaultTableColumnsProps,
  },
  {
    title: "Người tạo",
    dataIndex: "nguoi_tao",
    key: "nguoi_tao",
    align: "center",
    width: colWidthByKey.nguoi_tao,
    ...defaultTableColumnsProps,
  },
  {
    title: "Ngày cập nhật",
    dataIndex: "ngay_cap_nhat",
    key: "ngay_cap_nhat",
    align: "center",
    width: colWidthByKey.ngay_cap_nhat,
    ...defaultTableColumnsProps,
  },
  {
    title: "Người cập nhật",
    dataIndex: "nguoi_cap_nhat",
    key: "nguoi_cap_nhat",
    align: "center",
    width: colWidthByKey.nguoi_cap_nhat,
    ...defaultTableColumnsProps,
  },
  {
    title: "Trạng thái",
    dataIndex: "trang_thai_ten",
    key: "trang_thai_ten",
    align: "center",
    width: 130,
    ...defaultTableColumnsProps,
  },
];

/**
 * ===== TABLE COLUMN DATA INDEX TYPE - TYPE ĐỂ ĐỊNH NGHĨA CÁC KEY CỦA COLUMN =====
 * Type này được sử dụng để đảm bảo type safety khi truy cập các field của table data
 */
export type TableHangMucXeColumnDataIndex = keyof TableHangMucXeColumnDataType;
