import {FormInput} from "@src/components";
import {Col, Form, message, Row} from "antd";
import {Dayjs} from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle, useRef} from "react";
import {FormTaoMoiHopDongBaoHiemXeCoGioi, listHDVipSelect, listKieuHopDongSelect, ruleRequired, ThongTinHopDongXeStepProps} from "../Constant";
import ModalCanBoQuanLy, {IModalCanBoQuanLyRef} from "../ModalCanBoQuanLy";
import ModalDaiLyKhaiThac, {IModalDaiLyKhaiThacRef} from "../ModalDaiLyKhaiThac";
import ModalThongTinKhachHang, {IModalThongTinKhachHangRef} from "../ModalThongTinKhachHang";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, phong_ql, ma_kh, kieu_hd, so_hd_g, ma_cb_ql, ngay_cap, gio_hl, ngay_hl, gio_kt, ngay_kt, ma_sp, ma_ctbh, pt_kt, daily_kt, ma_nha_tpa, vip, so_hd} =
  FormTaoMoiHopDongBaoHiemXeCoGioi;
const ThongTinHopDongXeStepComponent = forwardRef<any, ThongTinHopDongXeStepProps>(
  (
    {
      listDoiTac,
      listChiNhanh,
      listPhongBan,
      listSanPham,
      listChuongTrinhBaoHiem,
      listPhuongThucKhaiThac,
      listDonViBoiThuongTPA,
      formNhapHopDongXe,
      khachHangSelected,
      setKhachHangSelected,
      daiLyKhaiThacSelected,
      setDaiLyKhaiThacSelected,
      onChangeDoiTac,
      canBoSeleceted,
      setCanBoSelected,
    }: ThongTinHopDongXeStepProps,
    ref,
  ) => {
    useImperativeHandle(ref, () => ({
      resetForm: () => {},
    }));

    // const [khachHangSelected, setKhachHangSelected] = useState<CommonExecute.Execute.IKhachHang | null>(null);

    let refModalThongTinKhachHang = useRef<IModalThongTinKhachHangRef>(null);
    let refModalDaiLyKhaiThac = useRef<IModalDaiLyKhaiThacRef>(null);
    let refModalCanBoQuanLy = useRef<IModalCanBoQuanLyRef>(null);

    //watch
    const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formNhapHopDongXe);
    const watchChiNhanhCapDon = Form.useWatch("ma_chi_nhanh_ql", formNhapHopDongXe);
    const watchKieuHopDong = Form.useWatch("kieu_hd", formNhapHopDongXe);

    //BẤM ĐỂ MỞ MODAL KHÁCH HÀNG
    const onClickInputKhachHang = () => {
      if (!watchChiNhanhCapDon || watchChiNhanhCapDon === "") {
        message.error("Vui lòng chọn chi nhánh cấp đơn!");
        return;
      }
      refModalThongTinKhachHang.current?.open(undefined, watchChiNhanhCapDon, watchDoiTacCapDon);
    };

    //BẤM ĐỂ MỞ MODAL CÁN BỘ QUẢN LÝ
    const onClickInputCanBoQuanLy = () => {
      if (!watchDoiTacCapDon || watchDoiTacCapDon === "") {
        message.error("Vui lòng chọn chi nhánh cấp đơn!");
        return;
      }
      refModalCanBoQuanLy.current?.open(watchChiNhanhCapDon, watchDoiTacCapDon);
    };

    //BẤM ĐỂ MỞ MODAL ĐẠI LÝ KHAI THẮC
    const onClickInputDaiLyKhaiThac = () => {
      if (!watchDoiTacCapDon || watchDoiTacCapDon === "") {
        message.error("Vui lòng chọn đối tác cấp đơn!");
        return;
      }
      refModalDaiLyKhaiThac.current?.open(undefined, watchDoiTacCapDon);
    };

    //Change Select Đối tác
    const onChangeMaDoiTac = (maDoiTac: string) => {
      formNhapHopDongXe.setFieldValue("ma_chi_nhanh_ql", undefined);
      formNhapHopDongXe.setFieldValue("phong_ql", undefined);
      formNhapHopDongXe.setFieldValue("pt_kt", undefined);
      const maChiNhanh = formNhapHopDongXe.getFieldValue("ma_chi_nhanh_ql");
      onChangeDoiTac(maDoiTac, maChiNhanh);
    };

    //Change Select Chi nhánh
    const onChangeMaChiNhanh = (maChiNhanh: string) => {
      formNhapHopDongXe.setFieldValue("phong_ql", undefined);
      const maDoiTac = formNhapHopDongXe.getFieldValue("ma_doi_tac_ql");
      onChangeDoiTac(maDoiTac, maChiNhanh);
    };

    // RENDER
    const renderFormInputColum = (props?: any, span = 6) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };

    const renderForm = () => (
      <Form form={formNhapHopDongXe} layout="vertical" className="mt-5">
        {/* gutter : khoảng cách giữa các ô */}
        <Row gutter={16}>
          {renderFormInputColum({
            ...ma_doi_tac_ql,
            options: listDoiTac,
            fieldNames: {label: "ten_tat", value: "ma"},
            onChange: (value: string) => {
              onChangeMaDoiTac(value);
            },
          })}
          {renderFormInputColum({
            ...ma_chi_nhanh_ql,
            options: listChiNhanh,
            fieldNames: {label: "ten_tat", value: "ma"},
            onChange: (value: string) => {
              onChangeMaChiNhanh(value);
            },
          })}
          {renderFormInputColum({...phong_ql, options: listPhongBan})}
          {renderFormInputColum({...ma_kh, options: [{ten: khachHangSelected?.ten, ma: khachHangSelected?.ma}], onClick: () => onClickInputKhachHang()})}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({
            ...so_hd,
          })}
          {renderFormInputColum({
            ...kieu_hd,
            options: listKieuHopDongSelect,
            onChange: (value: string) => {
              formNhapHopDongXe.setFieldValue("so_hd_g", undefined);
            },
          })}
          {renderFormInputColum({...so_hd_g, disabled: watchKieuHopDong === "G" ? true : false, rules: watchKieuHopDong === "G" ? [] : [ruleRequired]}, 3)}
          {renderFormInputColum({...vip, options: listHDVipSelect}, 3)}
          {renderFormInputColum({...ma_cb_ql, options: [{ten: canBoSeleceted?.ten, ma: canBoSeleceted?.ma || ""}], onClick: () => onClickInputCanBoQuanLy()})}

          {renderFormInputColum({
            ...ngay_cap,
            onChange: (date: Dayjs) => {
              //ngày hiệu lực = ngày cấp đơn, ngày kết thúc = ngày hiệu lực + 1 (năm)
              formNhapHopDongXe.setFields([
                {name: "ngay_hl", value: date},
                {name: "ngay_kt", value: date.add(1, "y")},
              ]);
            },
          })}
          {renderFormInputColum({...gio_hl}, 3)}
          {renderFormInputColum(
            {
              ...ngay_hl,
              onChange: (date: Dayjs) => {
                //ngày kết thúc = ngày hiệu lực + 1(năm)
                formNhapHopDongXe.setFields([{name: "ngay_kt", value: date.add(1, "y")}]);
              },
            },
            3,
          )}
          {renderFormInputColum({...gio_kt}, 3)}
          {renderFormInputColum({...ngay_kt}, 3)}
          {renderFormInputColum({...ma_sp, options: listSanPham})}
          {renderFormInputColum({...ma_ctbh, options: listChuongTrinhBaoHiem})}
          {renderFormInputColum({...pt_kt, options: listPhuongThucKhaiThac})}
          {renderFormInputColum({
            ...daily_kt,
            options: [{ten: daiLyKhaiThacSelected?.ten, ma: daiLyKhaiThacSelected?.ma}],
            onClick: () => onClickInputDaiLyKhaiThac(),
          })}
          {renderFormInputColum({...ma_nha_tpa, options: listDonViBoiThuongTPA})}
        </Row>
      </Form>
    );

    return (
      <>
        {/* Form nhập thông tin hợp đồng xe */}
        {renderForm()}
        {/* Modal */}
        <>
          <ModalThongTinKhachHang
            ref={refModalThongTinKhachHang}
            onSelectKhachHang={dataKhachang => {
              formNhapHopDongXe.setFieldValue("ma_kh", dataKhachang?.ma);
              setKhachHangSelected(dataKhachang || {ten: "", ma: ""});
            }}
          />
          <ModalDaiLyKhaiThac
            ref={refModalDaiLyKhaiThac}
            onSelectDaiLy={data => {
              formNhapHopDongXe.setFieldValue("daily_kt", data?.ma);
              setDaiLyKhaiThacSelected(data || {ten: "", ma: ""});
            }}
          />
          <ModalCanBoQuanLy
            ref={refModalCanBoQuanLy}
            onSelectCanBo={data => {
              formNhapHopDongXe.setFieldValue("ma_cb_ql", data?.ma);
              setCanBoSelected(data || {ten: "", ma: ""});
            }}
          />
        </>
      </>
    );
  },
);

ThongTinHopDongXeStepComponent.displayName = "ThongTinHopDongStepComponent";
export const ThongTinHopDongXeStep = memo(ThongTinHopDongXeStepComponent, isEqual);
