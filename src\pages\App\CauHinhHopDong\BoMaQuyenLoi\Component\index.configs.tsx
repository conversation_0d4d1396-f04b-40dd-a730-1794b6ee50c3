import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export interface IFormChiTietQuyenLoiFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ma_doi_tac_ql: IFormInput;
  nv: IFormInput;
  ten_e: IFormInput;
  loai: IFormInput;
  qloi_gh: IFormInput;
  ma_ct: IFormInput;
  bl_nt: IFormInput;
  bl_gt: IFormInput;
  bl_ra: IFormInput;
  ma_ql_th: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  ma_sp: IFormInput;
}

export const FormChiTietQuyenLoiConfigs: IFormChiTietQuyenLoiFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã quyền lợi",
    placeholder: "Mã quyền lợi",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên quyền lợi",
    placeholder: "Tên quyền lợi",
    rules: [ruleRequired],
  },
  ma_sp: {
    component: "select",
    name: "ma_sp",
    label: "Sản phẩm",
    placeholder: "Sản phẩm",
    rules: [ruleRequired],
  },
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác quản lý",
    placeholder: "Đối tác quản lý",
    rules: [ruleRequired],
  },
  nv: {
    component: "select",
    name: "nv",
    label: "Nghiệp vụ",
    placeholder: "Nghiệp vụ",
    rules: [ruleRequired],
  },
  loai: {
    component: "select",
    name: "loai",
    label: "Loại quyền lợi",
    placeholder: "Loại quyền lợi",
    rules: [ruleRequired],
  },
  qloi_gh: {
    component: "select",
    name: "qloi_gh",
    label: "Quyền lợi giới hạn",
    placeholder: "Quyền lợi giới hạn",
    rules: [ruleRequired],
  },
  ma_ct: {
    component: "select",
    name: "ma_ct",
    label: "Mã cấp trên",
    placeholder: "Mã cấp trên",
    showSearch: false,
  },
  bl_nt: {
    component: "checkbox",
    name: "bl_nt",
    label: "Bảo lãnh nội trú",
    placeholder: "Bảo lãnh nội trú",
  },
  bl_gt: {
    component: "checkbox",
    name: "bl_gt",
    label: "Bảo lãnh ngoại trú",
    placeholder: "Bảo lãnh ngoại trú",
  },
  bl_ra: {
    component: "checkbox",
    name: "bl_ra",
    label: "Bảo lãnh răng",
    placeholder: "Bảo lãnh răng",
  },
  stt: {
    component: "input",
    name: "stt",
    label: "STT",
    placeholder: "STT",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Trạng thái",
    rules: [ruleRequired],
  },
  ma_ql_th: {
    component: "input",
    name: "ma_ql_th",
    label: "Mã quyền lợi tổng hợp",
    placeholder: "Mã quyền lợi tổng hợp",
  },
  ten_e: {
    component: "input",
    name: "ten_e",
    label: "Tên quyền lợi (tiếng anh)",
    placeholder: "Tên quyền lợi (tiếng anh)",
  },
};

export const TRANG_THAI_TAO_MOI_QUYEN_LOI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];
export const QUYEN_LOI_GIOI_HAN = [
  {ten: " Quyền lợi chính(K) ", ma: "K"},
  {ten: " Quyền lợi giới hạn lần/ngày(NG)", ma: "NG"},
  {ten: " Quyền lợi giới hạn tiền lần/ngày(TI)", ma: "TI"},
  {ten: " Quyền lợi giới hạn số lần/ngày trong 1 lần điều trị(ND)", ma: "ND"},
  {ten: " Quyền lợi giới hạn số tiền trong 1 lần điều trị(TD)", ma: "TD"},
];
export const LOAI_QUYEN_LOI = [
  {ten: "Quyền lợi chính", ma: "CHINH"},
  {ten: "Quyền lợi bổ sung", ma: "DKSBS"},
];
export const NGHIEP_VU_THEM_MOI = [
  // {ten: "Tất cả", ma: ""},
  {ten: "Bảo hiểm con người", ma: "NG"},
  {ten: "Bảo hiểm xe cơ giới", ma: "XCG"},
  {ten: "Bảo hiểm tài sản", ma: "TS"},
];
export interface IQuyenLoiSelected {
  ma?: string;
  ten?: string;
}
export type TableQuyenLoiChaColumnDataIndex = keyof TableBoMaQuyenLoiChaDataType;
export interface ChiTetQuyenLoiProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listQuyenLoi: Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>;
}
export interface IModalChiTietQuyenLoiRef {
  open: (data?: CommonExecute.Execute.IChiTietBoMaQuyenLoi) => void;
  close: () => void;
}
export interface TableBoMaQuyenLoiChaDataType {
  key: string;
  stt: number;
  ma: string;
  ten: string;
  // doi_tac_ql_ten_tat: string;
  ma_ct: string;
  trang_thai_ten: string;
  nguoi_tao: string;
  ngay_tao: string;
  nguoi_cap_nhat: string;
  ngay_cap_nhat: string;
  ten_nv: string;
  ma_sp: string;
  nv: string;
  ma_doi_tac_ql: string;
  loai: string;
  qloi_gh: string;
  bl_nt: string;
  bl_gt: string;
  bl_ra: string;
  ma_dtac: string;
  trang_thai: string;
  ten_sp: string;
}

// const onHeaderCell = () => ({
//   className: "header-cell-custom",
// });
export const BoMaQuyenLoiChaColumns: TableProps<TableBoMaQuyenLoiChaDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã ", dataIndex: "ma", key: "ma", width: 80, align: "center", ...defaultTableColumnsProps},
  {title: "Tên quyền lợi", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Mã cấp trên", dataIndex: "ma_ct", key: "ma_ct", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Mã sản phẩm", dataIndex: "ma_sp", key: "ma_sp", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Loại quyền lợi", dataIndex: "loai", key: "loai", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "BL nội trú", dataIndex: "bl_nt", key: "bl_nt", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "BL ngoại trú", dataIndex: "bl_gt", key: "bl_gt", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "BL răng", dataIndex: "bl_ra", key: "bl_ra", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Q.lợi giới hạn", dataIndex: "qloi_gh", key: "qloi_gh", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Tên sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 200, align: "center", ...defaultTableColumnsProps},
  // {title: "Mã ql đối tác", dataIndex: "ma_dtac", key: "ma_dtac", width: 200, align: "center", ...defaultTableColumnsProps},
];
