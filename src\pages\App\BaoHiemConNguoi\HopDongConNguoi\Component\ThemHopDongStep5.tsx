import "@react-pdf-viewer/core/lib/styles/index.css";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {IThemHopDongStep5Ref, TableNguoiDuocBaoHiemColumnDataType, ThemHopDongStep5Props} from "./Constant";
import {Col, message, Row} from "antd";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {Viewer, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {formatCurrencyUS} from "@src/utils";
import {Button} from "@src/components";
import {FilePdfOutlined} from "@ant-design/icons";
import {DanhSachNguoiDuocBaoHiem} from "./ThemHopDongStep5_DanhSachNguoiDuocBaoHiem";
import {ReactQuery} from "@src/@types";

const ThemHopDongStep5Component = forwardRef<IThemHopDongStep5Ref, ThemHopDongStep5Props>(({}: ThemHopDongStep5Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {chiTietHopDong, chiTietNguoiDuocBaoHiem, timKiemPhanTrangNguoiDuocBaoHiemParams, getChiTietNguoiDuocBaoHiem, setTimKiemPhanTrangNguoiDuocBaoHiemParams, exportPdfHopDong} =
    useHopDongConNguoiContext();
  console.log("chiTietHopDong", chiTietHopDong);

  // State lưu selectedFile dựa trên categories thực tế
  const [selectedFile, setSelectedFile] = useState<any>(undefined);

  //khởi tạo dữ liệu();
  useEffect(() => {
    initData();
  }, []);

  const initData = useCallback(() => {
    setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, so_id: chiTietHopDong?.so_id, so_hd: chiTietHopDong?.so_hd, trang: 1, so_dong: 10});
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams, chiTietHopDong]);

  //bấm chọn 1 đối tượng - hiển thị GCN
  const handleSelectDoiTuongBaoHiemXe = useCallback(
    async (record: TableNguoiDuocBaoHiemColumnDataType) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      // const [chiTietDoiTuong] = await Promise.all([layChiTietDoiTuongBaoHiemXe(record as ReactQuery.IChiTietDoiTuongBaoHiemXeParams), handleViewGCN(record)]);
      // setDoiTuongXeSelected(chiTietDoiTuong || null);
      let response = await getChiTietNguoiDuocBaoHiem(record as ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi);
      viewGCNAsyncFn(record);
    },
    [getChiTietNguoiDuocBaoHiem],
  );

  // Ghi nhớ hàm async cho GCN để tránh vòng lặp vô hạn
  const viewGCNAsyncFn = useCallback(
    async (record: TableNguoiDuocBaoHiemColumnDataType) => {
      if (!record?.so_id_dt) {
        message.error("Không có thông tin đối tượng.");
        return;
      }

      try {
        const paramsXemFileGCN = {
          ma_doi_tac_ql: "ESCS",
          so_id: chiTietHopDong?.so_id,
          so_id_dt: record?.so_id_dt ? Number(record.so_id_dt) : undefined,
          template: chiTietHopDong?.file_gcn || "",
        };
        const pdfUrl = await exportPdfHopDong(paramsXemFileGCN);
        if (pdfUrl) {
          // Cập nhật selectedFile để hiển thị PDF GCN
          setSelectedFile({
            type: "pdf",
            url: pdfUrl,
            name: `GCN ${record?.so_id_dt}`,
          });
          message.success("Tải giấy chứng nhận thành công!");
        } else {
          console.error("❌ pdfUrl is null or undefined");
        }
      } catch (error) {
        console.error("Lỗi khi tải GCN PDF:", error);
        message.error("Có lỗi xảy ra khi tải giấy chứng nhận PDF!");
        throw error; // Re-throw để useAsyncAction có thể handle
      }
    },
    [chiTietHopDong, exportPdfHopDong],
  );

  // RENDER
  return (
    <Row gutter={16} className="mt-4 h-full overflow-hidden border border-[#eee]">
      {/* Cột trái: PDF Viewer với zoom/pan */}
      <Col span={11} className="flex h-[90%] flex-col overflow-hidden !pl-0" style={{borderBottom: "1px solid #eee", borderLeft: "1px solid #eee"}}>
        <div className="relative flex min-h-[65vh] flex-col overflow-hidden">
          {selectedFile && selectedFile.type === "pdf" ? (
            <div className="flex h-full w-full flex-col">
              {/* Zoom controls với thông tin zoom level và page navigation */}

              {/* PDF Viewer với zoom/pan tương tác */}
              <div
                // ref={containerRef}
                // className={`min-h-0 flex-1 overflow-hidden rounded-b-lg border border-[#333] shadow-[0_2px_8px_rgba(0,0,0,0.1)] ${
                //   zoomLevel > 1 ? (isPanning ? "cursor-grabbing" : "cursor-grab") : "cursor-default"
                // }`}
                // onMouseDown={handleMouseDown}
                // onMouseMove={handleMouseMove}
                // onMouseUp={handleMouseUp}
                // onMouseLeave={handleMouseLeave}
                // onDoubleClick={handleDoubleClick}
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                }}>
                <Worker workerUrl={workerUrl}>
                  <Viewer
                    fileUrl={selectedFile.url}
                    // plugins={[zoomPluginInstance]}
                    onDocumentLoad={e => {
                      // Reset zoom level và page info khi load document mới
                      //   setZoomLevel(1);
                      //   setCurrentPage(1);
                      //   setTotalPages(e.doc.numPages);
                    }}
                    onPageChange={e => {
                      // Cập nhật current page khi user navigate - chỉ để theo dõi, không can thiệp
                      //   setCurrentPage(e.currentPage + 1); // react-pdf-viewer sử dụng 0-based index
                    }}
                  />
                </Worker>
              </div>
            </div>
          ) : (
            <div className="flex h-full w-full flex-col">
              {/* Header placeholder để match với PDF viewer header */}
              <div className="flex items-center justify-between gap-2 rounded-t-lg border-b border-[#eee] bg-[#fafafa] p-2">
                <div className="flex items-center gap-2">
                  <div className="text-gray-600 bg-white rounded border px-2 py-1 text-xs">No PDF</div>
                </div>
              </div>
              {/* Main placeholder container để match với PDF viewer container */}
              <div className="flex min-h-0 flex-1 flex-col items-center justify-center overflow-hidden rounded-b-lg border border-[#333] text-center shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
                <div style={{fontSize: 48, opacity: 0.5, color: "#999"}}>📄</div>
                <div style={{marginBottom: 8, color: "#999", fontSize: 16}}>Chưa có file PDF nào được chọn</div>
              </div>
            </div>
          )}
        </div>
      </Col>

      {/* Cột giữa: Xem hình ảnh/PDF */}
      <Col span={7} className="flex h-[90%] flex-col overflow-hidden !px-0">
        {/* Thông tin chi tiết đối tượng xe được chọn */}
        {chiTietHopDong && (
          <div className="rounded border border-[#d9d9d9] bg-[#f6f6f6] p-3 shadow-sm">
            {/* <div style={{fontWeight: 600, fontSize: 16, marginBottom: 8}}>Thông tin đối tượng xe</div> */}
            {(() => {
              const thongTinHopDong = [
                {label: "Khách hàng", value: chiTietHopDong.ten_kh || "-"},
                {label: "Đối tác cấp đơn", value: chiTietHopDong.ten_doi_tac_ql || "-"},
                {label: "Số hợp đồng", value: chiTietHopDong.so_hd || "-"},
                {
                  label: "Hiệu lực",
                  value: chiTietHopDong.ngay_hl ? `${chiTietHopDong.gio_hl || ""} ${chiTietHopDong.ngay_hl} - ${chiTietHopDong.gio_kt || ""} ${chiTietHopDong.ngay_kt || ""}` : "-",
                },
                {label: "Loại hợp đồng", value: chiTietHopDong.vip || "Không Vip"},
                // Thêm các trường khác nếu cần
              ];

              // Thông tin sản phẩm và phí để hiển thị trên cùng một dòng
              const sanPham = chiTietHopDong.ten_sp || "-";
              const tongPhi = chiTietHopDong.tong_phi ? formatCurrencyUS(chiTietHopDong.tong_phi) : "-";
              return (
                <div>
                  <div className="flex flex-col gap-2">
                    {thongTinHopDong.map((item: any, idx: number) => (
                      <div key={idx} className="text-[11px]">
                        <b>{item.label}:</b> {item.value}
                      </div>
                    ))}
                    {/* Sản phẩm và Tổng phí BH trên cùng một dòng */}
                    <div className="flex gap-4 text-[11px]">
                      <span>
                        <b>Sản phẩm:</b> {sanPham}
                      </span>
                      <span>
                        <b>Tổng phí BH:</b> {tongPhi}
                      </span>
                    </div>
                  </div>
                  <div className="ml-0 mr-auto">
                    <Button
                      type="link"
                      className="!p-0 !font-semibold !text-[#7ac142]"
                      icon={<FilePdfOutlined />}
                      // onClick={handleViewHopDong}
                      //   loading={isLoadingViewHopDong}
                      //   disabled={isLoadingViewHopDong}
                    >
                      Xem hợp đồng
                    </Button>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
        {/* <ThongTinCauHinhBaoHiemXeStep disabled={true} pageSize={20} /> */}
      </Col>

      {/* Cột phải: Danh sách hạng mục và thumbnail */}
      <Col span={6} className="flex h-[90%] flex-col overflow-hidden">
        <DanhSachNguoiDuocBaoHiem
          onRowClick={handleSelectDoiTuongBaoHiemXe}
          // pageSize={dynamicPageSize}
        />
      </Col>
    </Row>
  );
});

ThemHopDongStep5Component.displayName = "ThemHopDongStep5Component";
export const ThemHopDongStep5 = memo(ThemHopDongStep5Component, isEqual);
