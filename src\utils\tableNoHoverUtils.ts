/**
 * Utility functions để loại bỏ hover effect cho Ant Design Table
 * Có thể tái sử dụng ở nhiều component khác nhau trong dự án
 */

// ==================== TYPES & INTERFACES ====================

/**
 * Interface cho cấu hình loại bỏ hover effect của Ant Design Table
 */
export interface ITableNoHoverConfig {
  /** Màu nền cho row active (mặc định: #96bf49) */
  activeRowColor?: string;
  /** Class name cho table container (mặc định: no-hover-effect) */
  tableClassName?: string;
  /** Class name cho row active (mặc định: table-row-active) */
  activeRowClassName?: string;
  /** ID cho style element để tránh duplicate (mặc định: table-no-hover-styles) */
  styleId?: string;
}

/**
 * Return type cho function injectTableNoHoverCSS
 */
export interface IInjectCSSResult {
  success: boolean;
  styleElement: HTMLStyleElement | null;
  isExisting?: boolean;
  error?: any;
}

// ==================== CONSTANTS ====================

/**
 * Cấu hình mặc định cho table no hover effect
 */
export const DEFAULT_TABLE_NO_HOVER_CONFIG: Required<ITableNoHoverConfig> = {
  activeRowColor: "#96bf49",
  tableClassName: "no-hover-effect",
  activeRowClassName: "table-row-active",
  styleId: "table-no-hover-styles",
};

/**
 * Preset configurations cho các use case phổ biến
 */
export const TABLE_NO_HOVER_PRESETS = {
  /** Màu xanh lá (mặc định) */
  GREEN: { activeRowColor: "#96bf49" },
  /** Màu xanh dương */
  BLUE: { activeRowColor: "#1890ff" },
  /** Màu xanh nhạt */
  LIGHT_BLUE: { activeRowColor: "#e6f4ff" },
  /** Màu vàng */
  YELLOW: { activeRowColor: "#fadb14" },
  /** Màu cam */
  ORANGE: { activeRowColor: "#fa8c16" },
  /** Màu đỏ */
  RED: { activeRowColor: "#ff4d4f" },
  /** Màu tím */
  PURPLE: { activeRowColor: "#722ed1" },
  /** Màu hồng */
  PINK: { activeRowColor: "#eb2f96" },
  /** Màu xám */
  GRAY: { activeRowColor: "#8c8c8c" },
} as const;

// ==================== CORE FUNCTIONS ====================

/**
 * Tạo CSS string để loại bỏ hover effect cho Ant Design Table
 * @param config - Cấu hình tùy chỉnh
 * @returns CSS string
 * 
 * @example
 * ```typescript
 * const css = createTableNoHoverCSS({ activeRowColor: "#1890ff" });
 * ```
 */
export const createTableNoHoverCSS = (config: ITableNoHoverConfig = {}): string => {
  const { activeRowColor, tableClassName, activeRowClassName } = {
    ...DEFAULT_TABLE_NO_HOVER_CONFIG,
    ...config,
  };

  return `
    /* Loại bỏ hover effect cho tất cả rows TRỪ row active với độ ưu tiên cao */
    .${tableClassName}.ant-table .ant-table-tbody tr:not(.${activeRowClassName}):hover,
    .${tableClassName}.ant-table .ant-table-tbody tr:not(.${activeRowClassName}):hover td,
    .${tableClassName} .ant-table-tbody tr:not(.${activeRowClassName}):hover,
    .${tableClassName} .ant-table-tbody tr:not(.${activeRowClassName}):hover td {
      background-color: transparent !important;
    }

    /* Đảm bảo row active có màu consistent với độ ưu tiên cao */
    .${tableClassName}.ant-table .ant-table-tbody tr.${activeRowClassName} td,
    .${tableClassName}.ant-table .ant-table-tbody tr.${activeRowClassName}:hover td,
    .${tableClassName} .ant-table-tbody tr.${activeRowClassName} td,
    .${tableClassName} .ant-table-tbody tr.${activeRowClassName}:hover td {
      background-color: ${activeRowColor} !important;
    }
  `;
};

/**
 * Inject CSS vào DOM để loại bỏ hover effect cho Ant Design Table
 * @param config - Cấu hình tùy chỉnh
 * @returns Object chứa thông tin về style element đã inject
 * 
 * @example
 * ```typescript
 * // Sử dụng cấu hình mặc định
 * const result = injectTableNoHoverCSS();
 * 
 * // Sử dụng màu tùy chỉnh
 * const result = injectTableNoHoverCSS({ activeRowColor: "#1890ff" });
 * 
 * // Sử dụng preset
 * const result = injectTableNoHoverCSS(TABLE_NO_HOVER_PRESETS.BLUE);
 * ```
 */
export const injectTableNoHoverCSS = (config: ITableNoHoverConfig = {}): IInjectCSSResult => {
  const finalConfig = { ...DEFAULT_TABLE_NO_HOVER_CONFIG, ...config };
  const { styleId } = finalConfig;

  // Kiểm tra môi trường browser
  if (typeof document === "undefined") {
    console.warn("injectTableNoHoverCSS: Document is not available (SSR environment)");
    return { success: false, styleElement: null };
  }

  // Kiểm tra xem style đã được inject chưa
  const existingStyle = document.querySelector(`style[data-style-id="${styleId}"]`) as HTMLStyleElement;
  if (existingStyle) {
    console.log(`injectTableNoHoverCSS: Style with ID "${styleId}" already exists`);
    return { success: true, styleElement: existingStyle, isExisting: true };
  }

  // Tạo và inject style element mới
  try {
    const styleElement = document.createElement("style");
    styleElement.textContent = createTableNoHoverCSS(config);
    styleElement.setAttribute("data-style-id", styleId);
    styleElement.setAttribute("data-description", "Ant Design Table No Hover Effect");
    document.head.appendChild(styleElement);

    console.log(`injectTableNoHoverCSS: Successfully injected style with ID "${styleId}"`);
    return { success: true, styleElement, isExisting: false };
  } catch (error) {
    console.error("injectTableNoHoverCSS: Failed to inject CSS", error);
    return { success: false, styleElement: null, error };
  }
};

// ==================== HELPER FUNCTIONS ====================

/**
 * Utility function để tạo className cho row dựa trên điều kiện active
 * @param isActive - Điều kiện để xác định row có active không
 * @param config - Cấu hình tùy chỉnh
 * @returns Class name string
 * 
 * @example
 * ```typescript
 * const className = getTableRowClassName(selectedId === record.id);
 * // Returns: "table-row-active" hoặc ""
 * ```
 */
export const getTableRowClassName = (
  isActive: boolean,
  config: ITableNoHoverConfig = {}
): string => {
  const { activeRowClassName } = { ...DEFAULT_TABLE_NO_HOVER_CONFIG, ...config };
  return isActive ? activeRowClassName : "";
};

/**
 * Utility function để tạo table className với no-hover-effect
 * @param additionalClasses - Các class khác cần thêm vào
 * @param config - Cấu hình tùy chỉnh
 * @returns Class name string đầy đủ
 * 
 * @example
 * ```typescript
 * const className = getTableClassName("custom-table bordered");
 * // Returns: "custom-table bordered no-hover-effect"
 * ```
 */
export const getTableClassName = (
  additionalClasses: string = "",
  config: ITableNoHoverConfig = {}
): string => {
  const { tableClassName } = { ...DEFAULT_TABLE_NO_HOVER_CONFIG, ...config };
  return additionalClasses ? `${additionalClasses} ${tableClassName}` : tableClassName;
};

/**
 * Remove style element đã inject trước đó
 * @param styleId - ID của style element cần remove
 * @returns Boolean indicating success
 * 
 * @example
 * ```typescript
 * const removed = removeTableNoHoverCSS("table-no-hover-styles");
 * ```
 */
export const removeTableNoHoverCSS = (styleId: string = DEFAULT_TABLE_NO_HOVER_CONFIG.styleId): boolean => {
  if (typeof document === "undefined") {
    console.warn("removeTableNoHoverCSS: Document is not available (SSR environment)");
    return false;
  }

  try {
    const styleElement = document.querySelector(`style[data-style-id="${styleId}"]`);
    if (styleElement) {
      styleElement.remove();
      console.log(`removeTableNoHoverCSS: Successfully removed style with ID "${styleId}"`);
      return true;
    } else {
      console.log(`removeTableNoHoverCSS: Style with ID "${styleId}" not found`);
      return false;
    }
  } catch (error) {
    console.error("removeTableNoHoverCSS: Failed to remove CSS", error);
    return false;
  }
};

// ==================== REACT HOOKS (Optional) ====================

/**
 * Custom hook để tự động inject và cleanup CSS cho table no hover effect
 * @param config - Cấu hình tùy chỉnh
 * @param enabled - Có enable hook này không (mặc định: true)
 * @returns Object chứa thông tin về style element và các utility functions
 * 
 * @example
 * ```typescript
 * const { tableClassName, rowClassName, isInjected } = useTableNoHover({
 *   activeRowColor: "#1890ff"
 * });
 * ```
 */
export const useTableNoHover = (config: ITableNoHoverConfig = {}, enabled: boolean = true) => {
  const finalConfig = { ...DEFAULT_TABLE_NO_HOVER_CONFIG, ...config };

  // Inject CSS khi component mount
  if (enabled && typeof window !== "undefined") {
    const result = injectTableNoHoverCSS(config);
    
    return {
      isInjected: result.success,
      styleElement: result.styleElement,
      tableClassName: finalConfig.tableClassName,
      activeRowClassName: finalConfig.activeRowClassName,
      getRowClassName: (isActive: boolean) => getTableRowClassName(isActive, config),
      getTableClassName: (additionalClasses?: string) => getTableClassName(additionalClasses, config),
    };
  }

  return {
    isInjected: false,
    styleElement: null,
    tableClassName: finalConfig.tableClassName,
    activeRowClassName: finalConfig.activeRowClassName,
    getRowClassName: (isActive: boolean) => getTableRowClassName(isActive, config),
    getTableClassName: (additionalClasses?: string) => getTableClassName(additionalClasses, config),
  };
};
