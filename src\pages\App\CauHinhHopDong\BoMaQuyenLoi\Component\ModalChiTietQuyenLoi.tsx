import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {forwardRef} from "react";
import {ChiTetQuyenLoiProps, FormChiTietQuyenLoiConfigs, IModalChiTietQuyenLoiRef, IQuyenLoiSelected, NGHIEP_VU_THEM_MOI, QUYEN_LOI_GIOI_HAN, TRANG_THAI_TAO_MOI_QUYEN_LOI} from "./index.configs";
import {FormInput, HeaderModal} from "@src/components";
import {useBoMaQuyenLoiContext} from "../index.context";
import {LOAI_QUYEN_LOI} from "../index.configs";
import {ReactQuery} from "@src/@types";
import {IModalTimQuyenLoiChaRef, ModalTimQuyenLoiCha} from "./ModalQuyenLoiCha";

const ModalChiTietQuyenLoiComponent = forwardRef<IModalChiTietQuyenLoiRef, ChiTetQuyenLoiProps>(({listDoiTac, listQuyenLoi}: ChiTetQuyenLoiProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataquyenloi?: CommonExecute.Execute.IChiTietBoMaQuyenLoi) => {
      setIsOpen(true);
      if (dataquyenloi) setDisableSubmit(true);

      if (dataquyenloi) setChiTietQuyenLoi(dataquyenloi);
    },
    close: () => setIsOpen(false),
  }));

  const [isOpen, setIsOpen] = useState(false);
  const [chiTietQuyenLoi, setChiTietQuyenLoi] = useState<CommonExecute.Execute.IChiTietBoMaQuyenLoi | null>(null);
  const {ma, ten, ma_doi_tac_ql, loai, qloi_gh, ma_ct, bl_nt, bl_gt, bl_ra, ten_e, nv, ma_sp, ma_ql_th, stt, trang_thai} = FormChiTietQuyenLoiConfigs;
  const {updateBoMaQuyenLoi, loading, listSanPham, getListSanPhamTheoDoiTac, setFilterParams, filterParams, danhSachBoMaQuyenLoi} = useBoMaQuyenLoiContext();
  const [formChiTietQuyenLoi] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], formChiTietQuyenLoi);
  const [quyenLoiSelected, setQuyenLoiSelected] = useState<IQuyenLoiSelected | null>(null);
  const refModelTimQuyenLoiCha = useRef<IModalTimQuyenLoiChaRef>(null);
  const watchDoiTac = Form.useWatch("ma_doi_tac_ql", formChiTietQuyenLoi);
  const watchNV = Form.useWatch("nv", formChiTietQuyenLoi);
  const watchMaSP = Form.useWatch("ma_sp", formChiTietQuyenLoi);
  console.log("filterparams chi tiết2", filterParams);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setFilterParams(filterParams);

    setChiTietQuyenLoi(null);
    formChiTietQuyenLoi.resetFields();
  }, [filterParams]);

  // init form data
  useEffect(() => {
    if (chiTietQuyenLoi) {
      // Gọi API nếu chưa có list sản phẩm đúng mã đối tác
      if (chiTietQuyenLoi.ma_doi_tac_ql && listSanPham.every(sp => sp.ma_doi_tac_ql !== chiTietQuyenLoi.ma_doi_tac_ql)) {
        getListSanPhamTheoDoiTac({ma_doi_tac_ql: chiTietQuyenLoi.ma_doi_tac_ql});
      }
      const transformedData = {
        ...chiTietQuyenLoi,
        bl_nt: chiTietQuyenLoi.bl_nt === "C",
        bl_gt: chiTietQuyenLoi.bl_gt === "C",
        bl_ra: chiTietQuyenLoi.bl_ra === "C",
      };
      const quyenLoiCha = listQuyenLoi.find(menu => menu.ma === chiTietQuyenLoi.ma_ct);
      if (quyenLoiCha) {
        setQuyenLoiSelected({
          ma: quyenLoiCha.ma,
          ten: quyenLoiCha.ten,
        });
      } else {
        setQuyenLoiSelected(null);
      }
      formChiTietQuyenLoi.setFieldsValue(transformedData);
    } else {
      formChiTietQuyenLoi.resetFields();
    }
  }, [chiTietQuyenLoi]);
  //xử lý validate form
  useEffect(() => {
    formChiTietQuyenLoi
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formChiTietQuyenLoi, formValues]);
  useEffect(() => {
    if (watchMaSP && watchNV) {
      const sanPham = listSanPham.find(sp => sp.ma === (watchMaSP?.value || watchMaSP));
      // setListSanPham();
      if (sanPham && sanPham.nv && sanPham.nv !== watchNV) {
        formChiTietQuyenLoi.setFields([
          {
            name: "nv",
            errors: ["Nghiệp vụ không trùng với nghiệp vụ của sản phẩm đã chọn!"],
          },
        ]);
      }
    }
    if (!watchDoiTac || !watchNV) {
      formChiTietQuyenLoi.setFieldValue("ma_sp", undefined);
    }
  }, [watchMaSP, watchNV, watchDoiTac]);
  const listSanPhamSelect = useMemo(() => {
    if (!watchDoiTac || !watchNV) return []; // nếu chưa chọn đối tác -> không hiển thị gì
    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTac && item.nv === watchNV);
  }, [listSanPham, watchDoiTac, watchNV]);

  // Fix for the getListSanPhamTheoDoiTac function call
  const handleDoiTacChange = async (value: any) => {
    try {
      const result = await getListSanPhamTheoDoiTac({ma_doi_tac_ql: value});
      // The function now properly returns the expected object
      return result;
    } catch (error) {
      console.error("Error fetching san pham:", error);
      return {data: [], tong_so_dong: 0};
    }
  };

  //Bấm Update

  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateBoMaQuyenLoiParams = formChiTietQuyenLoi.getFieldsValue(); //lấy ra values của form
      console.log("values", values);
      const finalValues = {
        ...values,
        bl_nt: values.bl_nt ? "C" : "K",
        bl_gt: values.bl_gt ? "C" : "K",
        bl_ra: values.bl_ra ? "C" : "K",
      };

      const response = await updateBoMaQuyenLoi(finalValues);
      // console.log("")
      if (response === -1) {
        closeModal();
        console.log("cập nhật thành công cácda");
      } else {
        console.log("cập nhật thất bại");
      }
      // await layDanhSachBoMaQuyenLoi(filterParams);
      // closeModal();
      //cập nhật lại danh mục quyền lợi
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const setErrorFormFields = useCallback(
    (name: string, errors: string[]) => {
      formChiTietQuyenLoi.setFields([
        {
          name,
          errors,
        },
      ]);
    },
    [formChiTietQuyenLoi],
  );
  //XỬ LÝ ĐIỀU KIỆN ĐỂ MỞ SELECT
  const checkDieuKienMoSelect = (inputName: string, isOpen: boolean) => {
    //NẾU CHƯA CÓ DATA ĐỐI TÁC MÀ CHỌN CÁC INPUT TƯƠNG ỨNG -> HIỂN THỊ LỖI
    const arrInputNameTheoDoiTacNV: Array<string> = ["ma_sp"];

    if (arrInputNameTheoDoiTacNV.includes(inputName) && isOpen)
      if (!watchDoiTac) {
        setErrorFormFields("ma_doi_tac_ql", ["Vui lòng chọn đối tác "]);
      }
    if (!watchNV) {
      setErrorFormFields("nv", ["Vui lòng chọn nghiệp vụ "]);
    }
  };
  //renderFooter Modal
  const renderFooter = () => {
    return (
      <Form.Item>
        {/* <Button type="default" htmlType="reset" form="formUpdateChiTietQuyenLoi" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button> */}

        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} onClick={onConfirm} icon={<CheckOutlined />}>
          Lưu
        </Button>
        {/* <Popcomfirm
          title="Thông báo"
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        /> */}
      </Form.Item>
    );
  };
  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  //renderForm Modal
  const renderForm = () => (
    <Form form={formChiTietQuyenLoi} layout="vertical" initialValues={{trang_thai: TRANG_THAI_TAO_MOI_QUYEN_LOI[0].ma}}>
      {/* MÃ */}
      <Row gutter={16}>
        {renderFormInputColum({...ma_doi_tac_ql, options: listDoiTac, onChange: handleDoiTacChange, disabled: chiTietQuyenLoi ? true : false}, 8)}
        {renderFormInputColum({...nv, options: NGHIEP_VU_THEM_MOI, disabled: chiTietQuyenLoi ? true : false}, 8)}
        {renderFormInputColum(
          {
            ...ma_sp,
            // labelInValue: true,
            options: listSanPhamSelect,
            onDropdownVisibleChange: (open: boolean) => checkDieuKienMoSelect(ma_sp.name, open),
            disabled: chiTietQuyenLoi ? true : false,
          },
          8,
        )}
      </Row>

      {/* gutter : khoảng cách giữa các ô */}
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormInputColum({...ma, disabled: chiTietQuyenLoi ? true : false}, 8)}
        {renderFormInputColum({...ten}, 8)}
        {renderFormInputColum(
          {
            ...loai,
            options: LOAI_QUYEN_LOI,
          },
          8,
        )}
      </Row>
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormInputColum({...ten_e}, 8)}
        {renderFormInputColum({...stt}, 8)}
        {renderFormInputColum(
          {
            ...trang_thai,
            options: TRANG_THAI_TAO_MOI_QUYEN_LOI,
          },
          8,
        )}
      </Row>
      <Row gutter={16}>
        {/* 3 cột có width = nhau  */}
        {renderFormInputColum(
          {
            ...qloi_gh,
            options: QUYEN_LOI_GIOI_HAN,
          },
          8,
        )}
        {renderFormInputColum(
          {
            ...ma_ct, // value: daiLyCha.ma,
            options: [{ma: quyenLoiSelected?.ma, ten: quyenLoiSelected?.ten}],
            // value: watchDLCha ? (typeof watchDLCha === "string" ? watchDLCha : watchDLCha.ma) : undefined,
            open: false,
            dropdownStyle: {display: "none"},
            // disabled: chitietDaiLy ? true : false,
            labelInValue: true,
            onClick: () => refModelTimQuyenLoiCha.current?.open(),
          },
          8,
        )}
        {renderFormInputColum({...ma_ql_th}, 8)}
      </Row>
      {watchNV === "NG" && (
        <Row gutter={16}>
          {renderFormInputColum(
            {
              ...bl_nt,
              valuePropName: "checked",
              getValueProps: (value: string) => ({checked: value === "C"}),
              getValueFromEvent: (e: any) => (e.target.checked ? "C" : "K"),
            },
            8,
          )}
          {renderFormInputColum(
            {
              ...bl_gt,
              valuePropName: "checked",
              getValueProps: (value: string) => ({checked: value === "C"}),
              getValueFromEvent: (e: any) => (e.target.checked ? "C" : "K"),
            },
            8,
          )}
          {renderFormInputColum(
            {
              ...bl_ra,
              valuePropName: "checked",
              getValueProps: (value: string) => ({checked: value === "C"}),
              getValueFromEvent: (e: any) => (e.target.checked ? "C" : "K"),
            },
            8,
          )}
        </Row>
      )}
    </Form>
  );
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        maskClosable={false}
        title={
          <HeaderModal
            title={chiTietQuyenLoi ? `Chi tiết quyền lợi ${chiTietQuyenLoi.ten}` : "Tạo mới quyền lợi"}
            trang_thai_ten={chiTietQuyenLoi?.trang_thai_ten}
            trang_thai={chiTietQuyenLoi?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "70%",
          sm: "70%",
          md: "70%",
          lg: "70%",
          xl: "70%",
          xxl: "70%",
        }}
        // styles={{
        //   body: {
        //     height: "100%",
        //     overflowY: "auto",
        //     overflowX: "hidden",
        //   },
        // }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
      <ModalTimQuyenLoiCha
        ref={refModelTimQuyenLoiCha}
        onSelectQuyenLoiCha={quyenLoiCha => {
          console.log("quyenloiCha", quyenLoiCha);
          if (quyenLoiCha) {
            formChiTietQuyenLoi.setFieldValue("ma_ct", quyenLoiCha.ma);
            setQuyenLoiSelected(quyenLoiCha);
          }
        }}
        chiTietQuyenLoi={chiTietQuyenLoi}
      />
    </Flex>
  );
});
ModalChiTietQuyenLoiComponent.displayName = "ModalChiTietQuyenLoiComponent";
export const ModalChiTietQuyenLoi = memo(ModalChiTietQuyenLoiComponent, isEqual);
