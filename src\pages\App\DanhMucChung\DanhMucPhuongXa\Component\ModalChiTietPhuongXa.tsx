import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState, useContext} from "react";
import {Button, Col, Flex, Form, Modal, Row} from "antd";
import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";

import {FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {IFormInput, ReactQuery} from "@src/@types";

import {useDanhMucPhuongXaContext} from "../index.context";
import {
  FormTaoMoiPhuongXa, 
  TRANG_THAI_TAO_MOI_PHUONG_XA,
  NGAY_AD_TAO_MOI_PHUONG_XA
} from "../index.configs";
import {IModalChiTietPhuongXaRef} from "./index.configs";

interface IModalChiTietPhuongXaProps {
  onAfterSave?: () => void;
  danhSachTinhThanh?: Array<{ma: string; ten: string}>; 
  danhSachQuanHuyen?: Array<{ma: string; ten: string}>; // Danh sách quận huyện cho dropdown
}

const ModalChiTietPhuongXaComponent = forwardRef<IModalChiTietPhuongXaRef, IModalChiTietPhuongXaProps>(
  ({onAfterSave, danhSachTinhThanh = [], danhSachQuanHuyen = []}, ref) => {
    // ===== CONTEXT & FORM =====
    const {capNhatChiTietPhuongXa, listPhuongXa, loading, getListQuanHuyen} = useDanhMucPhuongXaContext();
    const [form] = Form.useForm();

    // ===== STATE =====
    // Kiểm soát hiển thị/ẩn modal
    const [isOpen, setIsOpen] = useState<boolean>(false);
    // Dữ liệu phường xã đang được chỉnh sửa (null = tạo mới)
    const [chiTietPhuongXa, setChiTietPhuongXa] = useState<CommonExecute.Execute.IDanhMucPhuongXa | null>(null);
    // Kiểm soát button lưu có được enable hay không
    const [disableSubmit, setDisableSubmit] = useState<boolean>(true);
    // Danh sách quận huyện động theo tỉnh thành được chọn
    const [currentQuanHuyenOptions, setCurrentQuanHuyenOptions] = useState<Array<{ma: string; ten: string}>>(danhSachQuanHuyen);

    // ===== FORM VALUES WATCHING =====
    const formValues = Form.useWatch([], form);

    // ===== FORM CONFIGURATIONS =====
    const {ma_tinh, ma_quan, ngay_ad, ma, ten, postcode, stt, trang_thai} = FormTaoMoiPhuongXa;  // Đổi từ ma_quan_huyen thành ma_quan và thêm postcode

    // ===== DROPDOWN OPTIONS =====
    const tinhThanhOptions = danhSachTinhThanh.filter(item => item.ma !== ""); // Bỏ "Tất cả" option
    const trangThaiOptions = TRANG_THAI_TAO_MOI_PHUONG_XA;

    // Chức năng: Expose method để parent component có thể gọi mở modal từ bên ngoài
    useImperativeHandle(ref, () => ({
      open: async (data?: CommonExecute.Execute.IDanhMucPhuongXa) => {
        // Reset state trước khi set data mới
        setChiTietPhuongXa(null);
        setCurrentQuanHuyenOptions([]);
        form.resetFields();
        
        // Mở modal trước
        setIsOpen(true);
        
        // Sau đó set data
        if (data) {
          setChiTietPhuongXa(data);
          
          // Nếu có tỉnh thành, load quận huyện ngay lập tức
          if (data.ma_tinh) {
            try {
              await getListQuanHuyen({ma_tinh: data.ma_tinh});
            } catch (error) {
              // Xử lý lỗi thầm lặng
            }
          }
        }
      },
    }));

    // Chức năng: Điền dữ liệu vào form khi modal mở (chỉ cho create mode)
    useEffect(() => {
      if (!chiTietPhuongXa && isOpen) {
        // Tạo mới - set giá trị mặc định
        form.setFields([
          {name: "ma_tinh", value: undefined},
          {name: "ma_quan", value: undefined},    
          {name: "ma", value: ""},
          {name: "ten", value: ""},
          {name: "postcode", value: ""},          
          {name: "stt", value: ""},
          {name: "trang_thai", value: "D"}, 
        ]);
        setCurrentQuanHuyenOptions([]);
      }
    }, [chiTietPhuongXa, isOpen, form]); // Chỉ xử lý create mode

    // Chức năng: Cập nhật danh sách quận huyện khi props thay đổi từ provider
    useEffect(() => {
      const validOptions = danhSachQuanHuyen.filter(item => item.ma !== ""); // Bỏ "Tất cả" option
      
      // Chỉ update nếu thực sự có thay đổi
      setCurrentQuanHuyenOptions(prev => {
        // So sánh để tránh update không cần thiết
        if (JSON.stringify(validOptions) !== JSON.stringify(prev)) {
          return validOptions;
        }
        return prev;
      });
    }, [danhSachQuanHuyen]); // Bỏ currentQuanHuyenOptions khỏi dependency để tránh vòng lặp

    // Chức năng: Điền form khi quận huyện đã load xong cho edit mode
    useEffect(() => {
      if (chiTietPhuongXa && isOpen && danhSachQuanHuyen.length > 0) {
        // Chỉ điền form khi đang edit và có quận huyện từ props
        const validOptions = danhSachQuanHuyen.filter(item => item.ma !== "");
        if (validOptions.length > 0) {
          // Điền lại form data
          const arrFormData = [];
          for (const key in chiTietPhuongXa) {
            let fieldName = key;
            let fieldValue = chiTietPhuongXa[key as keyof CommonExecute.Execute.IDanhMucPhuongXa];
            
            // Xử lý conversion cho ngày áp dụng từ API format về dropdown format
            if (fieldName === 'ngay_ad' && fieldValue) {
              if (typeof fieldValue === 'string') {
                if (fieldValue === "01/01/1900") {
                  fieldValue = "19000101";
                } else if (fieldValue === "01/07/2025") {
                  fieldValue = "20250701";
                } else {
                  fieldValue = "19000101"; // default
                }
              } else if (typeof fieldValue === 'number') {
                fieldValue = fieldValue.toString();
              }
            }
            
            arrFormData.push({
              name: fieldName,
              value: fieldValue,
            });
          }
          form.setFields(arrFormData);
        }
      }
    }, [chiTietPhuongXa, isOpen, danhSachQuanHuyen, form]); // Trigger khi có quận huyện mới từ props

    // Chức năng: Re-validate form khi có thay đổi từ bên ngoài (chỉ cho edit mode)
    useEffect(() => {
      if (isOpen && chiTietPhuongXa && danhSachQuanHuyen.length > 0) {
        // Chỉ trigger re-validation khi đang edit và có dữ liệu quận huyện từ props
        setTimeout(() => {
          form.validateFields({validateOnly: true})
            .then(() => setDisableSubmit(false))
            .catch(() => setDisableSubmit(true));
        }, 100);
      }
    }, [danhSachQuanHuyen, isOpen, form, chiTietPhuongXa]); // Dùng danhSachQuanHuyen thay vì currentQuanHuyenOptions

    // Chức năng: Theo dõi form validation để enable/disable button lưu
    useEffect(() => {
      if (!isOpen) return;

      // Debounce validation để tránh gọi quá nhiều
      const timeoutId = setTimeout(() => {
        form
          .validateFields({validateOnly: true})
          .then(() => {
            setDisableSubmit(false);
          })
          .catch(() => {
            setDisableSubmit(true);
          });
      }, 300);

      return () => clearTimeout(timeoutId);
    }, [form, formValues, isOpen]);

    // Chức năng: Đóng modal và reset toàn bộ state về trạng thái ban đầu
    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietPhuongXa(null);
      setCurrentQuanHuyenOptions([]);
      form.resetFields();
    }, [form]);

    // Chức năng: Khi user chọn tỉnh thành khác, reset quận huyện và load danh sách quận huyện mới
    const handleTinhThanhChange = useCallback((value: any) => {
      // Reset quận huyện khi thay đổi tỉnh
      form.setFieldValue('ma_quan', undefined);    
      
      // Load quận huyện cho tỉnh mới
      if (value) {
        getListQuanHuyen({ma_tinh: value});
      } else {
        setCurrentQuanHuyenOptions([]);
      }
    }, [form, getListQuanHuyen]);

    // Chức năng: Xử lý lưu dữ liệu (tạo mới hoặc cập nhật), bao gồm validation trùng mã
    const onConfirm = async () => {
      try {
        const values: ReactQuery.ICapNhatDanhMucPhuongXaParams = form.getFieldsValue();

        // Validate mã phường xã không trùng khi tạo mới
        if (!chiTietPhuongXa) {
          for (let i = 0; i < listPhuongXa.length; i++) {
            if (listPhuongXa[i].ma === values.ma && 
                listPhuongXa[i].ma_tinh === values.ma_tinh && 
                listPhuongXa[i].ma_quan === values.ma_quan) {   
              form.setFields([
                {
                  name: "ma",
                  errors: ["Mã phường xã đã tồn tại trong quận/huyện này!"],
                },
              ]);
              return;
            }
          }
        }

        // Truyền thêm flag để phân biệt tạo mới vs cập nhật
        await capNhatChiTietPhuongXa(values, chiTietPhuongXa ? true : false);
        closeModal();
        onAfterSave?.();
      } catch (error) {
        console.error("Lỗi khi lưu phường xã:", error);
      }
    };


    // Chức năng: Render footer modal với button Quay lại và Lưu
    const renderFooter = () => {
      return (
        <Form.Item>
          <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
            Quay lại
          </Button>
          <Popcomfirm
            title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
            onConfirm={onConfirm}
            okText="Lưu"
            description="Bạn có chắc muốn lưu thông tin?"
            buttonTitle={"Lưu"}
            buttonDisable={disableSubmit}
            buttonClassName="w-40"
            buttonIcon={<CheckOutlined />}
            iconPosition="end"
            loading={loading}
          />
        </Form.Item>
      );
    };

    // Chức năng: Helper function để render 1 cột input trong form với custom column span
    const renderFormColumn = (props: any, colSpan: number = 8) => {
      return (
        <Col span={colSpan}>
          <FormInput {...props} />
        </Col>
      );
    };

    // Chức năng: Render form chính với layout các field phường xã
    const renderForm = () => (
      <Form form={form} layout="vertical">
        {/* TỈNH THÀNH, QUẬN HUYỆN, NGÀY ÁP DỤNG */}
        

        {/* MÃ PHƯỜNG XÃ, TÊN PHƯỜNG XÃ, MÃ BƯU ĐIỆN */}
        <Row gutter={16}>
        {renderFormColumn({
            ...ngay_ad, 
            options: NGAY_AD_TAO_MOI_PHUONG_XA,
            fieldNames: {label: "ten", value: "ma"},disabled: chiTietPhuongXa ? true : false
          })}
          {renderFormColumn({...ma, disabled: chiTietPhuongXa ? true : false})}
          {renderFormColumn({...ten})}
        </Row>

        <Row gutter={16}>
          {renderFormColumn({...ma_tinh, options: tinhThanhOptions, onChange: handleTinhThanhChange,disabled: chiTietPhuongXa ? true : false})}
          {renderFormColumn({...ma_quan, options: currentQuanHuyenOptions,disabled: chiTietPhuongXa ? true : false},8)} 
          {renderFormColumn({...postcode})}                               
        </Row>
        {/* STT, TRẠNG THÁI */}
        <Row gutter={16}>
          {renderFormColumn({...stt})}
          {renderFormColumn({
            ...trang_thai, 
            options: TRANG_THAI_TAO_MOI_PHUONG_XA
          })}
        </Row>
      </Form>
    );

    // ===== MAIN RENDER =====
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={
            <HeaderModal
              title={chiTietPhuongXa ? `${chiTietPhuongXa.ten}` : "Tạo mới phường xã"}
              trang_thai_ten={chiTietPhuongXa?.trang_thai_ten}
              trang_thai={chiTietPhuongXa?.trang_thai}
            />
          }
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={780}
          styles={{
            body: {
              paddingTop: "8px",
              paddingBottom: "16px",
            },
          }}
          footer={renderFooter()}
        >
          {renderForm()}
        </Modal>
      </Flex>
    );
  }
);

ModalChiTietPhuongXaComponent.displayName = "ModalChiTietPhuongXaComponent";
export const ModalChiTietPhuongXa = memo(ModalChiTietPhuongXaComponent, isEqual);
