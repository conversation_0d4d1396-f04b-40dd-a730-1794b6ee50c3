import {useEffect, useMemo, useRef, useState} from "react";
import {useBoMaQuyenLoiContext} from "./index.context";
import {BoMaQuyenLoiColumns, FormTimKiemPhanTrangBoMaQuyenLoi, NGHIEP_VU_TK, radioItemTrangThaiBoMaQuyenLoiTable, TableBoMaQuyenLoiDataType, TRANG_THAI_BO_MA_QUYEN_LOI} from "./index.configs";
import {Col, Flex, Form, Input, InputRef, Row, Table, TableColumnType, Tag} from "antd";
import {Button, FormInput, Highlighter} from "@src/components";
import {COLOR_PALETTE, ID_PAGE} from "@src/constants";
import {IFormInput, ReactQuery} from "@src/@types";
import {PlusCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {ClearOutlined} from "@ant-design/icons";
import {Tooltip} from "antd";
import "./index.default.scss";
import {ModalChiTietQuyenLoi} from "./Component/ModalChiTietQuyenLoi";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {IModalChiTietQuyenLoiRef} from "./Component/index.configs";
type DataIndex = keyof TableBoMaQuyenLoiDataType;

const {ma_doi_tac_ql, nd_tim, trang_thai, nv, ma_sp} = FormTimKiemPhanTrangBoMaQuyenLoi;
const BoMaQuyenLoiContent: React.FC = () => {
  const {danhSachBoMaQuyenLoi, loading, tongSoDong, getListSanPhamTheoDoiTac, layChiTietBoMaQuyenLoi, listSanPham, listDoiTac, filterParams, setFilterParams} = useBoMaQuyenLoiContext();
  const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams>(filterParams);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPaginationTableProps.defaultPageSize);
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [formTimKiemQuyenLoi] = Form.useForm();
  const refModalChiTietQuyenLoi = useRef<IModalChiTietQuyenLoiRef>(null);
  const watchDoiTac = Form.useWatch("ma_doi_tac_ql", formTimKiemQuyenLoi);
  const watchNV = Form.useWatch("nv", formTimKiemQuyenLoi);

  // const [danhSachBoMaQuyenLoi, setDanhSachBoMaQuyenLoi] = useState<Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>>([]);
  // const [tongSoDong, setTongSoDong] = useState<number>(0);
  // setDanhSachBoMaQuyenLoi(data);
  // setTongSoDong(response.data.tong_so_dong);
  //Watch value
  useEffect(() => {
    if (watchDoiTac && watchNV) {
      getListSanPhamTheoDoiTac({ma_doi_tac_ql: watchDoiTac, nv: watchNV});
    }
  }, [watchDoiTac, watchNV]);

  useEffect(() => {}, [listSanPham]);
  const onSearchApi = (values: ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams & ReactQuery.IPhanTrang) => {
    // đoạn này để sửa các giá trị undefined => '' trước khi request lên API
    // Ant Design Select không coi "" là một giá trị "chưa chọn", mà là một giá trị đã chọn hợp lệ (dù nó rỗng), nên nó sẽ không hiển thị placeholder nữa.
    const cleanedValues = {
      ...values,
      ma_doi_tac_ql: values.ma_doi_tac_ql ?? "",
      ma_sp: values.ma_sp ?? "",
      nd_tim: values.nd_tim ?? "",
      nv: values.nv ?? "",
      trang_thai: values.trang_thai ?? "",
      trang: values.trang,
      so_dong: values.so_dong,
    };
    setSearchParams(cleanedValues);
    // setPage();
    // setPageSize(pageSize);
    setFilterParams({...cleanedValues, trang: cleanedValues.trang, so_dong: defaultPaginationTableProps.defaultPageSize});
    // layDanhSachBoMaQuyenLoi({...filterParams, trang: 1, so_dong: pageSize});
    // setFilterParams(searchParams);
    console.log("filterparam", filterParams);
  };
  const onChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setPageSize(pageSize);
    // layDanhSachBoMaQuyenLoi({...filterParams, trang: page, so_dong: pageSize});
    setFilterParams({...filterParams, trang: page, so_dong: pageSize});
    console.log("params quyền lợi content", filterParams);
  };
  const listSanPhamSelect = useMemo(() => {
    if (!watchDoiTac || !watchNV) return [];

    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTac && item.nv === watchNV);
  }, [listSanPham, watchDoiTac, watchNV]);
  const handleSearch = (selectedKeys: string[], confirm: any, dataIndex: DataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };
  const handleReset = (clearFilters: any, confirm: any, dataIndex: DataIndex) => {
    clearFilters();
    setSearchText("");
    setSearchedColumn(dataIndex);
  };
  const dataTableListQuyenLoi = useMemo<Array<TableBoMaQuyenLoiDataType>>(() => {
    try {
      const tableData = danhSachBoMaQuyenLoi.map((item: any, index: number) => {
        return {
          key: index.toString(),
          stt: item.sott,
          ma: item.ma,
          ten: item.ten,
          doi_tac_ql_ten_tat: item.doi_tac_ql_ten_tat,
          trang_thai_ten: item.trang_thai_ten,
          nguoi_tao: item.nguoi_tao,
          ngay_tao: item.ngay_tao,
          nguoi_cap_nhat: item.nguoi_cap_nhat,
          ngay_cap_nhat: item.ngay_cap_nhat,
          ten_nv: item.ten_nv,
          loai: item.loai,
          ma_sp: item.ma_sp,
          nv: item.nv,
          qloi_gh: item.qloi_gh,
          bl_nt: item.bl_nt,
          bl_gt: item.bl_gt,
          bl_ra: item.bl_ra,
          ma_dtac: item.ma_dtac,
          trang_thai: item.trang_thai,
          ma_ct: item.ma_ct,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          ten_sp: item.ten_sp,
        };
      });
      const arrEmptyRow: Array<TableBoMaQuyenLoiDataType> = fillRowTableEmpty(tableData.length, defaultPaginationTableProps.defaultPageSize);
      return [...tableData, ...arrEmptyRow];
      // return tableData;
    } catch (error) {
      console.log("dataTableListQuyenLoi error", error);
      return [];
    }
  }, [danhSachBoMaQuyenLoi]);

  //tạo cột tìm kiếm
  const getColumnSearchProps = (dataIndex: DataIndex, title: string): TableColumnType<TableBoMaQuyenLoiDataType> => ({
    filterDropdown:
      dataIndex !== "trang_thai_ten"
        ? ({setSelectedKeys, selectedKeys, confirm, clearFilters, close}) => (
            <div style={{padding: 8}} onKeyDown={e => e.stopPropagation()} className="flex flex-row items-center p-2">
              <Input
                ref={searchInput}
                placeholder={`Tìm theo ${title}`}
                value={selectedKeys[0]}
                onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
                style={{display: "block", marginRight: 8}}
              />
              <Tooltip title="Tìm kiếm">
                <Button type="primary" shape="circle" icon={<SearchOutlined />} onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)} className="mr-2" />
              </Tooltip>
              <Tooltip title="Xoá">
                <Button type="primary" shape="circle" icon={<ClearOutlined />} onClick={() => clearFilters && handleReset(clearFilters, confirm, dataIndex)} />
              </Tooltip>
            </div>
          )
        : null,
    filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiBoMaQuyenLoiTable : undefined,
    render: (text, record, index) => {
      if (dataIndex === "trang_thai_ten") {
        // Xác định màu dựa vào text (trạng thái hiển thị)
        const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        if (record.key.toString().includes("empty")) return "";
        return <Tag color={color}>{text}</Tag>;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  const renderFormInputColum = (props: IFormInput, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderHeaderTableBoMaQuyenLoi = () => {
    return (
      <Form form={formTimKiemQuyenLoi} initialValues={{trang_thai: TRANG_THAI_BO_MA_QUYEN_LOI[0].ma}} layout="vertical" className="[&_.ant-form-item]:!mb-0" onFinish={onSearchApi}>
        <div className="flex flex-col gap-5">
          <Row gutter={16} align={"bottom"}>
            {renderFormInputColum({...nd_tim})}
            {renderFormInputColum({
              ...ma_doi_tac_ql,
              options: listDoiTac,
              onChange: value => getListSanPhamTheoDoiTac({ma_doi_tac_ql: value, nv: watchNV}),
            })}

            {renderFormInputColum({
              ...nv,
              options: NGHIEP_VU_TK,
            })}
            {renderFormInputColum({...ma_sp, options: listSanPhamSelect})}
            {renderFormInputColum({
              ...trang_thai,
              options: TRANG_THAI_BO_MA_QUYEN_LOI,
            })}
            <Col span={2}>
              <Form.Item>
                <Flex wrap="wrap" gap="small" className="w-full">
                  <Button className="w-full" htmlType="submit" type="primary" icon={<SearchOutlined />} loading={loading}>
                    Tìm kiếm
                  </Button>
                </Flex>
              </Form.Item>
            </Col>

            <Col span={2}>
              <Form.Item>
                <Flex wrap="wrap" gap="small" className="w-full">
                  <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => refModalChiTietQuyenLoi.current?.open()} loading={loading}>
                    Tạo mới
                  </Button>
                </Flex>
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>
    );
  };
  //render table
  return (
    <div id={ID_PAGE.BO_MA_QUYEN_LOI} className="[&_.ant-space]:w-full">
      <Table<TableBoMaQuyenLoiDataType>
        {...defaultTableProps}
        loading={loading}
        onRow={(record, rowIndex) => {
          return {
            onClick: async event => {
              if (record.key.toString().includes("empty")) return;
              const chiTietQuyenLoi = await layChiTietBoMaQuyenLoi({ma: record.ma, ma_doi_tac_ql: record.ma_doi_tac_ql, nv: record.nv, ma_sp: record.ma_sp});
              console.log("chiTietQuyenLoi", chiTietQuyenLoi);
              if (chiTietQuyenLoi) {
                refModalChiTietQuyenLoi.current?.open(chiTietQuyenLoi);
              }
            },
          };
        }}
        columns={(BoMaQuyenLoiColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as keyof TableBoMaQuyenLoiDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListQuyenLoi}
        title={renderHeaderTableBoMaQuyenLoi}
        pagination={{
          ...defaultPaginationTableProps,
          // defaultPageSize: 15,
          total: tongSoDong,
          onChange: (page, pageSize) => {
            onChangePage(page, pageSize);
          },
          // showLessItems: true, //Hiển thị ít mục trang hơn
          // defaultPageSize: 2, //Số lượng item dữ liệu mặc định trên mỗi trang
          // onShowSizeChange : Được gọi khi pageSize thay đổi
          // showTitle: false,// Hiển thị tiêu đề của item trang
          //showTotal : hiển thị UI bao nhiêu / bao nhiêu bản ghi
        }}
      />
      <ModalChiTietQuyenLoi ref={refModalChiTietQuyenLoi} listDoiTac={listDoiTac} listQuyenLoi={danhSachBoMaQuyenLoi} />
    </div>
  );
};
export default BoMaQuyenLoiContent;
