import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {useChiNhanh, useDoiTac} from "@src/hooks";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {FormChiTietKhachHang, LOAI_KHACH_HANG_OPTIONS, TRANG_THAI_MODAL_KHACH_HANG_OPTIONS} from "../index.configs";
import {useQuanLyKhachHangContext} from "../index.context";
import {ChiTietKhachHangProps, IModalChiTietKhachHangRef} from "./Constant";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, loai_kh, ma, ten, dchi, mst, cmt, dthoai, email, nguoi_lhe, dthoai_lhe, email_lhe, trang_thai} = FormChiTietKhachHang;

const ModalChiTietKhachHangComponent = forwardRef<IModalChiTietKhachHangRef, ChiTietKhachHangProps>(({listKhachHang}: ChiTietKhachHangProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataKhachHang?: CommonExecute.Execute.IKhachHang) => {
      setIsOpen(true);
      if (dataKhachHang) {
        setChiTietKhachHang(dataKhachHang); // set chi tiết khách hàng đã được load từ API
      }
    },
    close: () => setIsOpen(false),
  }));

  const [chiTietKhachHang, setChiTietKhachHang] = useState<CommonExecute.Execute.IKhachHang | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {luuThongTinKhachHang, layChiTietKhachHang, loading} = useQuanLyKhachHangContext();

  // Hooks để lấy danh sách đối tác và chi nhánh
  const {listDoiTac} = useDoiTac();
  const {listChiNhanh} = useChiNhanh();

  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(true); // Khởi tạo là true để disable button khi form trống
  const formValues = Form.useWatch([], form);

  // ===== FORM WATCHING =====
  const watchMaDoiTac = Form.useWatch("ma_doi_tac_ql", form); // Watch giá trị đối tác được chọn
  const isFirstRender = useRef(true); // Ref để track lần render đầu tiên

  // ===== COMPUTED VALUES =====
  /**
   * Lọc danh sách chi nhánh theo đối tác được chọn
   * Chỉ hiển thị các chi nhánh thuộc đối tác đã chọn
   */
  const listChiNhanhFilterTheoDoiTac = useMemo(() => {
    if (!watchMaDoiTac) {
      return listChiNhanh; // Nếu chưa chọn đối tác, hiển thị tất cả chi nhánh
    }
    const filtered = listChiNhanh.filter(item => item.ma_doi_tac === watchMaDoiTac);
    return filtered;
  }, [listChiNhanh, watchMaDoiTac]);

  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietKhachHang) {
      const arrFormData = [];
      for (const key in chiTietKhachHang) {
        arrFormData.push({
          name: key,
          value: chiTietKhachHang[key as keyof CommonExecute.Execute.IKhachHang],
        });
      }
      form.setFields(arrFormData);
    } else {
      // Khi tạo mới (chiTietKhachHang = null), đảm bảo form trống và button disable
      form.resetFields();
      setDisableSubmit(true);
    }
  }, [chiTietKhachHang, form]);

  // ===== SIDE EFFECTS =====
  /**
   * Xóa chi nhánh đã chọn khi thay đổi đối tác
   * Đảm bảo chi nhánh được chọn luôn thuộc đối tác hiện tại
   */
  useEffect(() => {
    // Bỏ qua lần đầu tiên để không xóa default values
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Chỉ validate khi dữ liệu đã load (listChiNhanh có dữ liệu)
    if (listChiNhanh.length === 0) {
      return;
    }

    const currentChiNhanh = form.getFieldValue("ma_chi_nhanh_ql");

    if (currentChiNhanh && watchMaDoiTac) {
      // Kiểm tra xem chi nhánh hiện tại có thuộc đối tác mới không
      const isChiNhanhBelongToDoiTac = listChiNhanhFilterTheoDoiTac.some(item => item.value === currentChiNhanh || item.ma === currentChiNhanh);

      // Nếu chi nhánh không thuộc đối tác mới, xóa selection
      if (!isChiNhanhBelongToDoiTac) {
        form.setFieldValue("ma_chi_nhanh_ql", undefined);
      }
    }
  }, [watchMaDoiTac, listChiNhanhFilterTheoDoiTac, form, listChiNhanh.length]);

  // Xử lý validate form - chạy khi modal mở hoặc form values thay đổi
  useEffect(() => {
    if (isOpen) {
      const validateForm = () => {
        form
          .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
          .then(() => {
            setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
          })
          .catch(() => {
            setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
          });
      };

      // Nếu là lần đầu mở modal, delay nhỏ để đảm bảo form đã được render
      if (formValues && Object.keys(formValues).length === 0) {
        setTimeout(validateForm, 100);
      } else {
        validateForm();
      }
    }
  }, [form, formValues, isOpen]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietKhachHang(null);
    form.resetFields();

    // Reset validation state về true (disable) vì form sẽ trống
    setDisableSubmit(true);

    // Clear form errors
    form.setFields(
      Object.keys(form.getFieldsValue()).map(name => ({
        name,
        errors: [],
      })),
    );
  }, [form]);

  //Bấm Lưu (không đóng modal)
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ILuuThongTinKhachHangParams = form.getFieldsValue(); //lấy ra values của form

      //với trường hợp tạo mới -> check mã khách hàng đã tồn tại
      if (!chiTietKhachHang) {
        for (let i = 0; i < listKhachHang.length; i++) {
          if (listKhachHang[i].ma === values.ma) {
            form.setFields([
              {
                name: "ma",
                errors: ["Mã khách hàng đã tồn tại!"],
              },
            ]);
            return;
          }
        }
      }

      const result = await luuThongTinKhachHang(values); //lưu thông tin khách hàng
      if (result) {
        // Lưu thành công -> gọi lại hàm chi tiết để cập nhật dữ liệu trong modal
        if (values.ma && values.ma_doi_tac_ql) {
          const updatedChiTietKhachHang = await layChiTietKhachHang({
            ma: values.ma,
            ma_doi_tac_ql: values.ma_doi_tac_ql,
            ma_chi_nhanh_ql: values.ma_chi_nhanh_ql,
          });

          if (updatedChiTietKhachHang) {
            setChiTietKhachHang(updatedChiTietKhachHang);
          }
        }
        // timKiemPhanTrangKhachHang đã được gọi trong luuThongTinKhachHang
      }
      // Nếu result = false, modal sẽ không đóng và user có thể thử lại
    } catch (error) {
      console.log("onConfirm error:", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0">
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" onClick={onConfirm} disabled={disableSubmit} loading={loading} icon={<CheckOutlined />} className="mr-2">
          Lưu
        </Button>
      </Form.Item>
    );
  };

  const renderFormColumn = (props: IFormInput) => (
    <Col span={6}>
      <FormInput {...props} />
    </Col>
  );

  const renderForm = () => (
    <Form form={form} layout="vertical">
      {/* Dòng 1: Đối tác quản lý, Chi nhánh quản lý, Loại khách hàng */}
      <Row gutter={16}>
        {renderFormColumn({
          ...ma_doi_tac_ql,
          options: listDoiTac,
          fieldNames: {label: "ten_tat", value: "ma"},
          disabled: chiTietKhachHang ? true : false,
        })}
        {renderFormColumn({
          ...ma_chi_nhanh_ql,
          options: listChiNhanhFilterTheoDoiTac,
          fieldNames: {label: "ten_tat", value: "ma"},
          disabled: chiTietKhachHang ? true : false,
        })}
        {renderFormColumn({
          ...loai_kh,
          options: LOAI_KHACH_HANG_OPTIONS,
        })}
        {renderFormColumn({...ma, disabled: chiTietKhachHang ? true : false})}
        {renderFormColumn({...ten})}
        {renderFormColumn({...dchi})}
        {renderFormColumn({...mst})}
        {renderFormColumn({...cmt})}
        {renderFormColumn({...dthoai})}
        {renderFormColumn({...email})}
        {renderFormColumn({...nguoi_lhe})}
        {renderFormColumn({...dthoai_lhe})}
        {renderFormColumn({...email_lhe})}
        {renderFormColumn({
          ...trang_thai,
          options: TRANG_THAI_MODAL_KHACH_HANG_OPTIONS,
        })}
      </Row>
    </Form>
  );

  //Render
  return (
    <Flex vertical align="flex-start">
      <Modal
        className="modal-chi-tiet-khach-hang"
        title={<HeaderModal title={chiTietKhachHang ? `${chiTietKhachHang.ten}` : "Tạo mới khách hàng"} trang_thai_ten={chiTietKhachHang?.trang_thai_ten} trang_thai={chiTietKhachHang?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={"80%"}
        styles={{
          body: {
            overflowY: "auto",
            overflowX: "hidden",
          },
        }}
        footer={renderFooter()}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietKhachHangComponent.displayName = "ModalChiTietKhachHangComponent";
export const ModalChiTietKhachHang = memo(ModalChiTietKhachHangComponent, isEqual);
